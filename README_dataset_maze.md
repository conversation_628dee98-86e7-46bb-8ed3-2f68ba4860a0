# 数据集迷宫加载功能 Dataset Maze Loading

本文档介绍如何使用数据集中的迷宫布局进行多智能体SLAM仿真。

This document explains how to use maze layouts from the dataset for multi-agent SLAM simulation.

## 功能概述 Overview

新增的数据集迷宫加载功能允许您：
- 从 `/data1/fangzr/Research/Memory_Maze_3D/Dataset/self-navigation-mini/` 中随机选择迷宫布局
- 支持 D1_P1 到 D1_P30 共30个不同的迷宫配置
- 自动加载迷宫布局图像和环境配置信息
- 与现有的多智能体SLAM验证框架无缝集成

The new dataset maze loading functionality allows you to:
- Randomly select maze layouts from the dataset directory
- Support 30 different maze configurations (D1_P1 to D1_P30)
- Automatically load maze layout images and environment configurations
- Seamlessly integrate with the existing multi-agent SLAM validation framework

## 快速开始 Quick Start

### 1. 基本使用 Basic Usage

```python
from main_validator import MultiAgentSLAMValidator
from navigation_strategies import ExplorationNavigationStrategy

# 创建使用数据集迷宫的验证器
validator = MultiAgentSLAMValidator(
    maze_size=15,
    num_agents=3,
    seed=42,
    use_dataset_maze=True  # 启用数据集迷宫加载
)

# 选择导航策略
strategy = ExplorationNavigationStrategy()

# 初始化并运行仿真
validator.initialize_simulation(strategy)
results = validator.run_simulation(max_steps=150, record_video=True)

# 可视化和导出结果
validator.visualize_results("results.png")
validator.export_results("results.json")
```

### 2. 运行演示脚本 Run Demo Scripts

```bash
# 测试数据集迷宫加载
python test_dataset_maze.py load

# 测试数据集迷宫仿真
python test_dataset_maze.py sim

# 比较生成迷宫与数据集迷宫
python test_dataset_maze.py compare

# 运行交互式演示
python demo_dataset_maze.py
```

### 3. 使用主验证器 Using Main Validator

```bash
# 使用数据集迷宫运行演示
python main_validator.py dataset

# 测试多个数据集迷宫
python main_validator.py test_multiple

# 使用数据集迷宫运行完整演示
python main_validator.py dataset_demo
```

## 数据集结构 Dataset Structure

数据集目录结构：
```
/data1/fangzr/Research/Memory_Maze_3D/Dataset/self-navigation-mini/
├── D1_P1/
│   ├── 000000/
│   │   ├── env_config.json      # 环境配置文件
│   │   ├── frame_info.json      # 帧信息
│   │   ├── bev_views/           # 鸟瞰图视图
│   │   └── fps_views/           # 第一人称视图
│   └── path_visualization/
│       ├── maze_layout_000000.png    # 迷宫布局图像
│       ├── maze_path_000000.png      # 路径可视化
│       └── path_coords_000000.txt    # 路径坐标
├── D1_P2/
├── ...
└── D1_P30/
```

## API 参考 API Reference

### MultiAgentSLAMValidator

新增参数：
- `use_dataset_maze: bool = False` - 是否使用数据集迷宫
- `dataset_path: Optional[str] = None` - 数据集路径（默认为标准路径）

### RoomBasedMazeGenerator

新增方法：
- `load_random_dataset_maze() -> Tuple[np.ndarray, dict]` - 随机加载数据集迷宫
- `_load_maze_from_png(png_path: str) -> Optional[np.ndarray]` - 从PNG文件加载迷宫
- `_reconstruct_maze_from_config(config: dict) -> np.ndarray` - 从配置重建迷宫

## 配置选项 Configuration Options

### 环境配置文件 Environment Config

每个数据集迷宫包含一个 `env_config.json` 文件，包含：
```json
{
    "env_name": "memory_maze:MemoryMaze-15x15-ExtraObs",
    "agent_height": 0.3,
    "fov_angle": 75.0,
    "seed": 1,
    "maze_size": 15,
    "maze_config": {
        "maze_size": 15,
        "max_rooms": 6,
        "room_min_size": 3,
        "room_max_size": 5,
        "spawns_per_room": 1,
        "objects_per_room": 1
    },
    "objects": [...]
}
```

### 迷宫加载优先级 Maze Loading Priority

1. 首先尝试从 `maze_layout_000000.png` 加载迷宫布局
2. 如果PNG加载失败，尝试从 `env_config.json` 重建迷宫
3. 如果都失败，回退到生成随机迷宫

## 输出文件 Output Files

使用数据集迷宫时，输出文件会包含额外信息：
- 结果JSON文件中包含 `maze_source: "dataset"` 标识
- 包含原始数据集的配置信息
- 可视化图像显示实际的数据集迷宫布局

## 故障排除 Troubleshooting

### 常见问题 Common Issues

1. **数据集路径不存在**
   - 确保数据集路径正确：`/data1/fangzr/Research/Memory_Maze_3D/Dataset/self-navigation-mini/`
   - 检查目录权限

2. **PNG文件加载失败**
   - 系统会自动回退到配置文件重建
   - 检查PIL库是否正确安装：`pip install Pillow`

3. **JSON序列化错误**
   - 这是已知问题，不影响主要功能
   - 结果仍会正确保存和可视化

### 依赖要求 Dependencies

确保安装以下依赖：
```bash
pip install numpy matplotlib opencv-python Pillow
```

## 示例输出 Example Output

成功加载数据集迷宫时的输出：
```
从数据集加载迷宫: D1_P15
使用数据集迷宫，配置: memory_maze:MemoryMaze-15x15-ExtraObs
迷宫尺寸: (15, 15)
通路占比: 46.22%
环境名称: memory_maze:MemoryMaze-15x15-ExtraObs
种子: 1
对象数量: 6
```

## 性能对比 Performance Comparison

数据集迷宫与生成迷宫的主要区别：
- **数据集迷宫**：来自真实的Memory Maze环境，布局更加复杂和真实
- **生成迷宫**：程序化生成，结构相对简单但可控性更强
- **探索难度**：数据集迷宫通常具有更高的探索挑战性
- **一致性**：数据集迷宫提供标准化的测试环境

## 贡献 Contributing

如需添加新功能或修复问题，请：
1. 确保代码与现有架构兼容
2. 添加适当的错误处理
3. 更新相关文档
4. 测试所有功能正常工作
