#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SentryNet完整演示脚本
Complete SentryNet Demo Script

演示SentryNet的完整工作流程：
1. 数据收集
2. 模型训练
3. SPC分析
4. 集成测试
"""

import os
import sys
import argparse
from typing import Optional

# 导入各个模块
from sentry_data_collector import SentryDataCollector, DataCollectionConfig
from sentry_trainer import SentryTrainer, TrainingConfig
from sentry_net import SentryNetConfig
from spc_analyzer import SPCAnalyzer, SPCAnalysisConfig
from sentry_integration_test import SentryIntegrationTester, IntegrationTestConfig


def run_data_collection(config: Optional[DataCollectionConfig] = None) -> bool:
    """运行数据收集"""
    print("=" * 60)
    print("步骤1: 数据收集")
    print("=" * 60)
    
    if config is None:
        config = DataCollectionConfig(
            num_episodes=50,  # 适中的数据量
            steps_per_episode=150
        )
    
    try:
        collector = SentryDataCollector(config)
        collector.collect_all_data()
        collector.save_data()
        
        # 显示统计信息
        stats = collector.get_data_statistics()
        print("\n数据收集统计:")
        for key, value in stats.items():
            print(f"  {key}: {value}")
        
        return True
    except Exception as e:
        print(f"数据收集失败: {e}")
        return False


def run_training(sentry_config: Optional[SentryNetConfig] = None,
                training_config: Optional[TrainingConfig] = None) -> bool:
    """运行模型训练"""
    print("=" * 60)
    print("步骤2: 模型训练")
    print("=" * 60)
    
    if sentry_config is None:
        sentry_config = SentryNetConfig()
    
    if training_config is None:
        training_config = TrainingConfig(
            num_epochs=50,  # 适中的训练轮数
            batch_size=16,
            learning_rate=1e-3
        )
    
    try:
        trainer = SentryTrainer(sentry_config, training_config)
        trainer.train()
        return True
    except Exception as e:
        print(f"模型训练失败: {e}")
        return False


def run_spc_analysis(config: Optional[SPCAnalysisConfig] = None) -> bool:
    """运行SPC分析"""
    print("=" * 60)
    print("步骤3: SPC分析")
    print("=" * 60)
    
    if config is None:
        config = SPCAnalysisConfig(
            analysis_episodes=10,
            steps_per_episode=80
        )
    
    try:
        analyzer = SPCAnalyzer(config)
        results = analyzer.run_analysis()
        
        print("\nSPC分析结果:")
        report = results['report']
        for key, value in report.items():
            print(f"  {key}: {value}")
        
        return True
    except Exception as e:
        print(f"SPC分析失败: {e}")
        return False


def run_integration_test(config: Optional[IntegrationTestConfig] = None) -> bool:
    """运行集成测试"""
    print("=" * 60)
    print("步骤4: 集成测试")
    print("=" * 60)
    
    if config is None:
        config = IntegrationTestConfig(
            test_episodes=3,
            steps_per_episode=100
        )
    
    try:
        tester = SentryIntegrationTester(config)
        results = tester.run_all_tests()
        
        print("\n集成测试结果:")
        summary = results['summary_stats']
        print(f"  总警告数: {summary['total_warnings']}")
        print(f"  总惩罚数: {summary['total_penalties']}")
        print(f"  警告有效性: {summary['warning_effectiveness']:.2f}")
        
        return True
    except Exception as e:
        print(f"集成测试失败: {e}")
        return False


def check_dependencies() -> bool:
    """检查依赖项"""
    required_modules = ['torch', 'numpy', 'matplotlib', 'sklearn', 'scipy', 'seaborn']
    missing_modules = []
    
    for module in required_modules:
        try:
            __import__(module)
        except ImportError:
            missing_modules.append(module)
    
    if missing_modules:
        print("缺少以下依赖项:")
        for module in missing_modules:
            print(f"  - {module}")
        print("\n请使用以下命令安装:")
        print(f"pip install {' '.join(missing_modules)}")
        return False
    
    return True


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="SentryNet完整演示")
    parser.add_argument('--step', type=str, choices=['all', 'collect', 'train', 'analyze', 'test'],
                       default='all', help='要运行的步骤')
    parser.add_argument('--quick', action='store_true', help='快速演示模式（减少数据量和训练轮数）')
    parser.add_argument('--skip-existing', action='store_true', help='跳过已存在的文件')
    
    args = parser.parse_args()
    
    print("SentryNet - 社交哨兵网络演示")
    print("=" * 60)
    print("这个演示将展示SentryNet的完整工作流程：")
    print("1. 从多智能体仿真环境收集同伴观察数据")
    print("2. 使用自监督学习训练SentryNet")
    print("3. 分析训练后的社交位置细胞(SPC)激活模式")
    print("4. 在实际环境中测试社会监控功能")
    print("=" * 60)
    
    # 检查依赖项
    if not check_dependencies():
        return
    
    # 配置参数（快速模式使用更小的参数）
    if args.quick:
        print("使用快速演示模式...")
        data_config = DataCollectionConfig(num_episodes=10, steps_per_episode=50)
        sentry_config = SentryNetConfig()
        training_config = TrainingConfig(num_epochs=10, batch_size=8)
        analysis_config = SPCAnalysisConfig(analysis_episodes=5, steps_per_episode=30)
        test_config = IntegrationTestConfig(test_episodes=2, steps_per_episode=50)
    else:
        data_config = None
        sentry_config = None
        training_config = None
        analysis_config = None
        test_config = None
    
    success_count = 0
    total_steps = 0
    
    # 步骤1: 数据收集
    if args.step in ['all', 'collect']:
        total_steps += 1
        if args.skip_existing and os.path.exists("sentry_training_data/training_sequences.pkl"):
            print("跳过数据收集（文件已存在）")
            success_count += 1
        else:
            if run_data_collection(data_config):
                success_count += 1
    
    # 步骤2: 模型训练
    if args.step in ['all', 'train']:
        total_steps += 1
        if args.skip_existing and os.path.exists("sentry_models/best_model.pth"):
            print("跳过模型训练（文件已存在）")
            success_count += 1
        else:
            # 检查数据是否存在
            if not os.path.exists("sentry_training_data/training_sequences.pkl"):
                print("警告: 训练数据不存在，先运行数据收集...")
                if run_data_collection(data_config):
                    success_count += 1
                else:
                    print("数据收集失败，跳过训练")
                    total_steps -= 1
            
            if os.path.exists("sentry_training_data/training_sequences.pkl"):
                if run_training(sentry_config, training_config):
                    success_count += 1
    
    # 步骤3: SPC分析
    if args.step in ['all', 'analyze']:
        total_steps += 1
        if not os.path.exists("sentry_models/best_model.pth"):
            print("警告: 训练好的模型不存在，跳过SPC分析")
            total_steps -= 1
        else:
            if run_spc_analysis(analysis_config):
                success_count += 1
    
    # 步骤4: 集成测试
    if args.step in ['all', 'test']:
        total_steps += 1
        if not os.path.exists("sentry_models/best_model.pth"):
            print("警告: 训练好的模型不存在，跳过集成测试")
            total_steps -= 1
        else:
            if run_integration_test(test_config):
                success_count += 1
    
    # 总结
    print("\n" + "=" * 60)
    print("演示完成!")
    print(f"成功完成: {success_count}/{total_steps} 步骤")
    
    if success_count == total_steps:
        print("\n🎉 所有步骤都成功完成！")
        print("\n生成的文件:")
        
        files_to_check = [
            ("sentry_training_data/", "训练数据"),
            ("sentry_models/", "训练好的模型"),
            ("spc_analysis_plots/", "SPC分析图表"),
            ("sentry_test_results/", "集成测试结果")
        ]
        
        for path, description in files_to_check:
            if os.path.exists(path):
                print(f"  ✓ {path} - {description}")
            else:
                print(f"  ✗ {path} - {description} (未生成)")
        
        print("\n你可以查看以下内容:")
        print("  - spc_analysis_plots/ 中的SPC激活模式分析")
        print("  - sentry_test_results/ 中的集成测试视频和结果")
        print("  - sentry_models/training_curves.png 中的训练曲线")
        
    else:
        print(f"\n⚠️  有 {total_steps - success_count} 个步骤失败")
        print("请检查错误信息并重新运行相应步骤")
    
    print("=" * 60)


if __name__ == "__main__":
    main()
