# -*- coding: utf-8 -*-
# File: navigation_strategies.py
#!/usr/bin/env python3
"""
导航策略模块 - 实现不同的智能体导航策略
Navigation Strategies - Implements different agent navigation strategies
"""

import numpy as np
import random
import math
from typing import Tuple, List
from abc import ABC, abstractmethod
from agent_manager import AgentState


class BaseNavigationStrategy(ABC):
    """导航策略基类"""
    
    def __init__(self, step_size: float = 0.5, turn_angle: float = 0.3):
        self.step_size = step_size
        self.turn_angle = turn_angle
    
    @abstractmethod
    def select_next_move(self, agent_state: AgentState, ground_truth_maze: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """
        选择下一步移动
        Args:
            agent_state: 智能体状态
            ground_truth_maze: 真实迷宫地图
        Returns:
            Tuple[new_position, new_direction]: 新位置和新朝向
        """
        pass
    
    @abstractmethod
    def get_name(self) -> str:
        """获取策略名称"""
        pass
    
    def _is_valid_position(self, position: np.ndarray, maze: np.ndarray) -> bool:
        """检查位置是否有效（不穿墙）"""
        x, y = position[0], position[1]
        maze_size = maze.shape[0]
        
        # 边界检查
        if x < 0.5 or x >= maze_size - 0.5 or y < 0.5 or y >= maze_size - 0.5:
            return False
        
        # 墙壁检查
        grid_x, grid_y = int(x), int(y)
        if maze[grid_y, grid_x] == 0:  # 0为墙壁
            return False
        
        return True
    
    def _rotate_direction(self, direction: np.ndarray, angle: float) -> np.ndarray:
        """旋转方向向量"""
        cos_a = math.cos(angle)
        sin_a = math.sin(angle)
        
        new_x = direction[0] * cos_a - direction[1] * sin_a
        new_y = direction[0] * sin_a + direction[1] * cos_a
        
        return np.array([new_x, new_y])
    
    def _normalize_direction(self, direction: np.ndarray) -> np.ndarray:
        """归一化方向向量"""
        norm = np.linalg.norm(direction)
        if norm == 0:
            return np.array([1.0, 0.0])  # 默认朝右
        return direction / norm


class RandomNavigationStrategy(BaseNavigationStrategy):
    """随机导航策略"""
    
    def select_next_move(self, agent_state: AgentState, ground_truth_maze: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        current_pos = agent_state.position
        current_dir = agent_state.direction
        
        # 随机选择动作：前进、左转、右转、后退
        action = random.choice(['forward', 'turn_left', 'turn_right', 'backward'])
        
        new_direction = current_dir.copy()
        new_position = current_pos.copy()
        
        if action == 'forward':
            # 前进
            new_position = current_pos + current_dir * self.step_size
        elif action == 'backward':
            # 后退
            new_position = current_pos - current_dir * self.step_size
        elif action == 'turn_left':
            # 左转
            new_direction = self._rotate_direction(current_dir, self.turn_angle)
            new_position = current_pos + new_direction * self.step_size * 0.5  # 转向时小步前进
        elif action == 'turn_right':
            # 右转
            new_direction = self._rotate_direction(current_dir, -self.turn_angle)
            new_position = current_pos + new_direction * self.step_size * 0.5
        
        # 检查新位置是否有效
        if not self._is_valid_position(new_position, ground_truth_maze):
            # 如果无效，尝试原地转向
            if random.random() < 0.5:
                new_direction = self._rotate_direction(current_dir, self.turn_angle)
            else:
                new_direction = self._rotate_direction(current_dir, -self.turn_angle)
            new_position = current_pos
        
        return new_position, self._normalize_direction(new_direction)
    
    def get_name(self) -> str:
        return "Random"


class ExplorationNavigationStrategy(BaseNavigationStrategy):
    """探索导向导航策略"""
    
    def __init__(self, step_size: float = 0.5, turn_angle: float = 0.3):
        super().__init__(step_size, turn_angle)
        self.stuck_counter = {}  # 记录每个智能体的卡住次数
        self.last_positions = {}  # 记录上一次位置
    
    def select_next_move(self, agent_state: AgentState, ground_truth_maze: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        agent_id = agent_state.agent_id
        current_pos = agent_state.position
        current_dir = agent_state.direction
        local_map = agent_state.local_map
        
        # 检查是否卡住
        is_stuck = self._check_if_stuck(agent_id, current_pos)
        
        if is_stuck:
            # 如果卡住，执行随机逃脱动作
            return self._escape_action(current_pos, current_dir, ground_truth_maze)
        
        # 寻找最有价值的探索方向
        best_direction = self._find_best_exploration_direction(
            current_pos, current_dir, local_map, ground_truth_maze
        )
        
        if best_direction is not None:
            # 朝最佳方向移动
            target_angle = math.atan2(best_direction[1], best_direction[0])
            current_angle = math.atan2(current_dir[1], current_dir[0])
            angle_diff = target_angle - current_angle
            
            # 标准化角度差
            while angle_diff > math.pi:
                angle_diff -= 2 * math.pi
            while angle_diff < -math.pi:
                angle_diff += 2 * math.pi
            
            # 根据角度差决定动作
            if abs(angle_diff) < 0.2:  # 方向接近，直接前进
                new_position = current_pos + current_dir * self.step_size
                new_direction = current_dir
            elif angle_diff > 0:  # 需要左转
                new_direction = self._rotate_direction(current_dir, min(self.turn_angle, angle_diff))
                new_position = current_pos + new_direction * self.step_size * 0.3
            else:  # 需要右转
                new_direction = self._rotate_direction(current_dir, max(-self.turn_angle, angle_diff))
                new_position = current_pos + new_direction * self.step_size * 0.3
        else:
            # 没有找到好的探索方向，随机移动
            return RandomNavigationStrategy().select_next_move(agent_state, ground_truth_maze)
        
        # 验证新位置
        if self._is_valid_position(new_position, ground_truth_maze):
            self.last_positions[agent_id] = current_pos.copy()
            return new_position, self._normalize_direction(new_direction)
        else:
            # 位置无效，尝试转向
            if random.random() < 0.5:
                new_direction = self._rotate_direction(current_dir, self.turn_angle)
            else:
                new_direction = self._rotate_direction(current_dir, -self.turn_angle)
            return current_pos, self._normalize_direction(new_direction)
    
    def _check_if_stuck(self, agent_id: int, current_pos: np.ndarray) -> bool:
        """检查智能体是否卡住"""
        if agent_id not in self.last_positions:
            self.stuck_counter[agent_id] = 0
            return False
        
        last_pos = self.last_positions[agent_id]
        movement = np.linalg.norm(current_pos - last_pos)
        
        if movement < 0.1:  # 移动距离很小
            self.stuck_counter[agent_id] = self.stuck_counter.get(agent_id, 0) + 1
            return self.stuck_counter[agent_id] > 5  # 连续5步移动很小认为卡住
        else:
            self.stuck_counter[agent_id] = 0
            return False
    
    def _escape_action(self, current_pos: np.ndarray, current_dir: np.ndarray, 
                      ground_truth_maze: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """执行逃脱动作"""
        # 大角度随机转向
        turn_angle = random.uniform(-math.pi/2, math.pi/2)
        new_direction = self._rotate_direction(current_dir, turn_angle)
        
        # 尝试向新方向移动
        new_position = current_pos + new_direction * self.step_size
        
        if self._is_valid_position(new_position, ground_truth_maze):
            return new_position, self._normalize_direction(new_direction)
        else:
            # 如果还是无效，只转向不移动
            return current_pos, self._normalize_direction(new_direction)
    
    def _find_best_exploration_direction(self, current_pos: np.ndarray, current_dir: np.ndarray,
                                       local_map: np.ndarray, ground_truth_maze: np.ndarray) -> np.ndarray:
        """寻找最佳探索方向"""
        maze_size = local_map.shape[0]
        
        # 寻找未知区域
        unknown_positions = []
        for y in range(maze_size):
            for x in range(maze_size):
                if local_map[y, x] == -1:  # 未知区域
                    # 检查是否在可达范围内
                    target_pos = np.array([x + 0.5, y + 0.5])
                    distance = np.linalg.norm(target_pos - current_pos)
                    if distance < 8.0:  # 在合理范围内
                        unknown_positions.append(target_pos)
        
        if not unknown_positions:
            return None
        
        # 寻找前沿点（已知区域边缘的未知点）
        frontier_positions = []
        for target_pos in unknown_positions:
            grid_x, grid_y = int(target_pos[0]), int(target_pos[1])
            
            # 检查周围是否有已知区域
            has_known_neighbor = False
            for dx in [-1, 0, 1]:
                for dy in [-1, 0, 1]:
                    nx, ny = grid_x + dx, grid_y + dy
                    if (0 <= nx < maze_size and 0 <= ny < maze_size and 
                        local_map[ny, nx] != -1):
                        has_known_neighbor = True
                        break
                if has_known_neighbor:
                    break
            
            if has_known_neighbor:
                frontier_positions.append(target_pos)
        
        # 优先选择前沿点
        candidate_positions = frontier_positions if frontier_positions else unknown_positions
        
        # 选择最近的候选点
        distances = [np.linalg.norm(pos - current_pos) for pos in candidate_positions]
        min_idx = np.argmin(distances)
        best_target = candidate_positions[min_idx]
        
        # 计算方向
        direction_to_target = best_target - current_pos
        return self._normalize_direction(direction_to_target)
    
    def get_name(self) -> str:
        return "Exploration"


class WallFollowingStrategy(BaseNavigationStrategy):
    """墙壁跟随策略"""
    
    def __init__(self, step_size: float = 0.5, turn_angle: float = 0.3):
        super().__init__(step_size, turn_angle)
        self.following_left_wall = {}  # 记录是否在跟随左墙
    
    def select_next_move(self, agent_state: AgentState, ground_truth_maze: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        agent_id = agent_state.agent_id
        current_pos = agent_state.position
        current_dir = agent_state.direction
        
        # 初始化墙壁跟随方向
        if agent_id not in self.following_left_wall:
            self.following_left_wall[agent_id] = random.choice([True, False])
        
        follow_left = self.following_left_wall[agent_id]
        
        # 检查各个方向
        front_pos = current_pos + current_dir * self.step_size
        left_dir = self._rotate_direction(current_dir, math.pi/2)
        right_dir = self._rotate_direction(current_dir, -math.pi/2)
        left_pos = current_pos + left_dir * self.step_size
        right_pos = current_pos + right_dir * self.step_size
        
        front_valid = self._is_valid_position(front_pos, ground_truth_maze)
        left_valid = self._is_valid_position(left_pos, ground_truth_maze)
        right_valid = self._is_valid_position(right_pos, ground_truth_maze)
        
        if follow_left:
            # 左手法则
            if left_valid:
                # 左边可通行，转向左边
                new_direction = left_dir
                new_position = left_pos
            elif front_valid:
                # 前方可通行，继续前进
                new_direction = current_dir
                new_position = front_pos
            else:
                # 左边和前方都不通，右转
                new_direction = right_dir
                new_position = current_pos
        else:
            # 右手法则
            if right_valid:
                # 右边可通行，转向右边
                new_direction = right_dir
                new_position = right_pos
            elif front_valid:
                # 前方可通行，继续前进
                new_direction = current_dir
                new_position = front_pos
            else:
                # 右边和前方都不通，左转
                new_direction = left_dir
                new_position = current_pos
        
        return new_position, self._normalize_direction(new_direction)
    
    def get_name(self) -> str:
        return "WallFollowing"


class FrontierExplorationStrategy(BaseNavigationStrategy):
    """前沿点探索策略"""
    
    def __init__(self, step_size: float = 0.5, turn_angle: float = 0.3):
        super().__init__(step_size, turn_angle)
        self.target_frontiers = {}  # 每个智能体的目标前沿点
    
    def select_next_move(self, agent_state: AgentState, ground_truth_maze: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        agent_id = agent_state.agent_id
        current_pos = agent_state.position
        current_dir = agent_state.direction
        local_map = agent_state.local_map
        
        # 寻找前沿点
        frontiers = self._find_frontiers(local_map)
        
        if not frontiers:
            # 没有前沿点，使用探索策略
            return ExplorationNavigationStrategy().select_next_move(agent_state, ground_truth_maze)
        
        # 选择目标前沿点
        if (agent_id not in self.target_frontiers or 
            self._reached_target(current_pos, self.target_frontiers[agent_id])):
            # 选择新的目标前沿点
            self.target_frontiers[agent_id] = self._select_best_frontier(current_pos, frontiers)
        
        target_frontier = self.target_frontiers[agent_id]
        
        # 朝目标前沿点移动
        direction_to_target = target_frontier - current_pos
        target_angle = math.atan2(direction_to_target[1], direction_to_target[0])
        current_angle = math.atan2(current_dir[1], current_dir[0])
        angle_diff = target_angle - current_angle
        
        # 标准化角度差
        while angle_diff > math.pi:
            angle_diff -= 2 * math.pi
        while angle_diff < -math.pi:
            angle_diff += 2 * math.pi
        
        # 根据角度差决定动作
        if abs(angle_diff) < 0.3:  # 方向接近，前进
            new_position = current_pos + current_dir * self.step_size
            new_direction = current_dir
        elif angle_diff > 0:  # 左转
            new_direction = self._rotate_direction(current_dir, min(self.turn_angle, angle_diff))
            new_position = current_pos + new_direction * self.step_size * 0.5
        else:  # 右转
            new_direction = self._rotate_direction(current_dir, max(-self.turn_angle, angle_diff))
            new_position = current_pos + new_direction * self.step_size * 0.5
        
        # 验证新位置
        if self._is_valid_position(new_position, ground_truth_maze):
            return new_position, self._normalize_direction(new_direction)
        else:
            # 位置无效，原地转向
            if angle_diff > 0:
                new_direction = self._rotate_direction(current_dir, self.turn_angle)
            else:
                new_direction = self._rotate_direction(current_dir, -self.turn_angle)
            return current_pos, self._normalize_direction(new_direction)
    
    def _find_frontiers(self, local_map: np.ndarray) -> List[np.ndarray]:
        """寻找前沿点"""
        frontiers = []
        maze_size = local_map.shape[0]
        
        for y in range(1, maze_size - 1):
            for x in range(1, maze_size - 1):
                if local_map[y, x] == -1:  # 未知区域
                    # 检查是否有已知的自由空间邻居
                    neighbors = [
                        local_map[y-1, x], local_map[y+1, x],
                        local_map[y, x-1], local_map[y, x+1]
                    ]
                    if any(n == 1 for n in neighbors):  # 有自由空间邻居
                        frontiers.append(np.array([x + 0.5, y + 0.5]))
        
        return frontiers
    
    def _select_best_frontier(self, current_pos: np.ndarray, frontiers: List[np.ndarray]) -> np.ndarray:
        """选择最佳前沿点"""
        if len(frontiers) == 1:
            return frontiers[0]
        
        # 选择距离最近的前沿点
        distances = [np.linalg.norm(f - current_pos) for f in frontiers]
        min_idx = np.argmin(distances)
        return frontiers[min_idx]
    
    def _reached_target(self, current_pos: np.ndarray, target: np.ndarray, threshold: float = 1.0) -> bool:
        """检查是否达到目标"""
        return np.linalg.norm(current_pos - target) < threshold
    
    def get_name(self) -> str:
        return "FrontierExploration"


if __name__ == "__main__":
    # 测试导航策略
    from agent_manager import AgentManager, AgentState
    import matplotlib.pyplot as plt
    
    # 创建测试环境
    maze_size = 10
    test_maze = np.ones((maze_size, maze_size), dtype=np.uint8)
    test_maze[3:6, 3:6] = 0
    test_maze[1:3, 7:9] = 0
    
    # 创建测试智能体
    agent_state = AgentState(
        agent_id=0,
        position=np.array([2.0, 2.0]),
        direction=np.array([1.0, 0.0]),
        sector=0,
        local_map=np.full((maze_size, maze_size), -1, dtype=np.int8),
        fused_map=np.full((maze_size, maze_size), -1, dtype=np.int8),
        exploration_area=0.0,
        reward_score=0.0,
        step_count=0
    )
    
    # 测试不同策略
    strategies = [
        RandomNavigationStrategy(),
        ExplorationNavigationStrategy(),
        WallFollowingStrategy(),
        FrontierExplorationStrategy()
    ]
    
    for strategy in strategies:
        print(f"\n测试策略: {strategy.get_name()}")
        
        # 重置智能体位置
        agent_state.position = np.array([2.0, 2.0])
        agent_state.direction = np.array([1.0, 0.0])
        
        # 模拟10步移动
        trajectory = [agent_state.position.copy()]
        
        for step in range(10):
            new_pos, new_dir = strategy.select_next_move(agent_state, test_maze)
            agent_state.position = new_pos
            agent_state.direction = new_dir
            trajectory.append(new_pos.copy())
            
            print(f"  Step {step}: Position {new_pos}, Direction {new_dir}")
        
        # 可视化轨迹
        trajectory = np.array(trajectory)
        plt.figure(figsize=(8, 6))
        plt.imshow(test_maze, cmap='gray', origin='lower', alpha=0.7)
        plt.plot(trajectory[:, 0], trajectory[:, 1], 'r-o', markersize=6, linewidth=2)
        plt.plot(trajectory[0, 0], trajectory[0, 1], 'go', markersize=10, label='Start')
        plt.plot(trajectory[-1, 0], trajectory[-1, 1], 'ro', markersize=10, label='End')
        plt.title(f'{strategy.get_name()} Strategy Trajectory')
        plt.legend()
        plt.grid(True, alpha=0.3)
        plt.show()