#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SentryNet - 社交哨兵网络
Social Sentry Network for detecting peer boundary violations

这个模块实现了SentryNet，一个专门用于社会监控的神经网络。
它能够通过观察同伴的原始感官信息流，学会判断同伴是否处于关键区域（如领地边界），
并生成功能性的社交位置细胞(SPC)表征。
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import Tuple, Dict, Optional
from dataclasses import dataclass


@dataclass
class SentryNetConfig:
    """SentryNet配置参数"""
    # 输入维度
    patch_size: int = 7  # 局部地图切片大小 7x7
    num_channels: int = 2  # 地图通道数 (墙壁/通路, 未知区域)
    velocity_dim: int = 2  # 速度向量维度 (vx, vy)
    
    # 网络架构参数
    cnn_hidden_dim: int = 32  # CNN隐藏层维度
    fusion_hidden_dim: int = 64  # 融合层隐藏维度
    memory_hidden_dim: int = 128  # GRU隐藏状态维度
    
    # 功能头参数
    attention_key_dim: int = 64  # 注意力Key向量维度
    message_value_dim: int = 64  # 消息Value向量维度
    
    # 训练参数
    dropout_rate: float = 0.1
    device: str = "cuda" if torch.cuda.is_available() else "cpu"


class ObservationEncoder(nn.Module):
    """观察编码器 - 使用CNN处理同伴的局部地图切片"""
    
    def __init__(self, config: SentryNetConfig):
        super().__init__()
        self.config = config
        
        # 两层卷积网络
        self.conv1 = nn.Conv2d(
            in_channels=config.num_channels,
            out_channels=config.cnn_hidden_dim // 2,
            kernel_size=3,
            padding=1
        )
        self.conv2 = nn.Conv2d(
            in_channels=config.cnn_hidden_dim // 2,
            out_channels=config.cnn_hidden_dim,
            kernel_size=3,
            padding=1
        )
        
        # 全局平均池化
        self.global_pool = nn.AdaptiveAvgPool2d(1)
        self.dropout = nn.Dropout(config.dropout_rate)
        
    def forward(self, peer_local_patch: torch.Tensor) -> torch.Tensor:
        """
        前向传播
        Args:
            peer_local_patch: [batch_size, num_channels, patch_size, patch_size]
        Returns:
            encoded_features: [batch_size, cnn_hidden_dim]
        """
        # 第一层卷积 + ReLU
        x = F.relu(self.conv1(peer_local_patch))
        x = self.dropout(x)
        
        # 第二层卷积 + ReLU
        x = F.relu(self.conv2(x))
        x = self.dropout(x)
        
        # 全局平均池化，将空间维度压缩为1x1
        x = self.global_pool(x)  # [batch_size, cnn_hidden_dim, 1, 1]
        x = x.view(x.size(0), -1)  # [batch_size, cnn_hidden_dim]
        
        return x


class InputFusion(nn.Module):
    """输入融合层 - 融合空间特征和动态信息"""
    
    def __init__(self, config: SentryNetConfig):
        super().__init__()
        self.config = config
        
        # 融合网络
        input_dim = config.cnn_hidden_dim + config.velocity_dim
        self.fusion_net = nn.Sequential(
            nn.Linear(input_dim, config.fusion_hidden_dim),
            nn.ReLU(),
            nn.Dropout(config.dropout_rate),
            nn.Linear(config.fusion_hidden_dim, config.fusion_hidden_dim),
            nn.ReLU(),
            nn.Dropout(config.dropout_rate)
        )
        
    def forward(self, spatial_features: torch.Tensor, peer_velocity: torch.Tensor) -> torch.Tensor:
        """
        前向传播
        Args:
            spatial_features: [batch_size, cnn_hidden_dim] CNN编码的空间特征
            peer_velocity: [batch_size, velocity_dim] 同伴速度向量
        Returns:
            fused_features: [batch_size, fusion_hidden_dim]
        """
        # 拼接空间特征和速度信息
        combined = torch.cat([spatial_features, peer_velocity], dim=1)
        
        # 通过融合网络
        fused_features = self.fusion_net(combined)
        
        return fused_features


class MemoryModule(nn.Module):
    """记忆模块 - 使用GRU整合时间序列信息"""
    
    def __init__(self, config: SentryNetConfig):
        super().__init__()
        self.config = config
        
        # GRU层
        self.gru = nn.GRU(
            input_size=config.fusion_hidden_dim,
            hidden_size=config.memory_hidden_dim,
            batch_first=True
        )
        
        self.dropout = nn.Dropout(config.dropout_rate)
        
    def forward(self, fused_features: torch.Tensor, 
                hidden_state: Optional[torch.Tensor] = None) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        前向传播
        Args:
            fused_features: [batch_size, fusion_hidden_dim] 融合后的特征
            hidden_state: [1, batch_size, memory_hidden_dim] 上一时刻的隐藏状态
        Returns:
            belief_state: [batch_size, memory_hidden_dim] 当前信念状态
            new_hidden_state: [1, batch_size, memory_hidden_dim] 新的隐藏状态
        """
        # 为GRU添加序列维度
        if fused_features.dim() == 2:
            fused_features = fused_features.unsqueeze(1)  # [batch_size, 1, fusion_hidden_dim]
        
        # 通过GRU
        output, new_hidden_state = self.gru(fused_features, hidden_state)
        
        # 移除序列维度
        belief_state = output.squeeze(1)  # [batch_size, memory_hidden_dim]
        belief_state = self.dropout(belief_state)
        
        return belief_state, new_hidden_state


class PredictiveDecoder(nn.Module):
    """预测解码器 - 预测同伴下一时刻的局部地图"""
    
    def __init__(self, config: SentryNetConfig):
        super().__init__()
        self.config = config
        
        # 解码网络：从信念状态到空间特征
        self.decode_net = nn.Sequential(
            nn.Linear(config.memory_hidden_dim, config.fusion_hidden_dim),
            nn.ReLU(),
            nn.Dropout(config.dropout_rate),
            nn.Linear(config.fusion_hidden_dim, config.cnn_hidden_dim),
            nn.ReLU(),
            nn.Dropout(config.dropout_rate)
        )
        
        # 反卷积网络：从特征到图像
        self.deconv_net = nn.Sequential(
            # 首先将特征reshape为小的特征图
            nn.ConvTranspose2d(config.cnn_hidden_dim, config.cnn_hidden_dim // 2, 
                             kernel_size=3, padding=1),
            nn.ReLU(),
            nn.ConvTranspose2d(config.cnn_hidden_dim // 2, config.num_channels, 
                             kernel_size=3, padding=1),
            nn.Sigmoid()  # 输出概率
        )
        
    def forward(self, belief_state: torch.Tensor) -> torch.Tensor:
        """
        前向传播
        Args:
            belief_state: [batch_size, memory_hidden_dim] 信念状态
        Returns:
            predicted_patch: [batch_size, num_channels, patch_size, patch_size] 预测的局部地图
        """
        # 解码到特征
        features = self.decode_net(belief_state)  # [batch_size, cnn_hidden_dim]
        
        # Reshape为特征图
        patch_size = self.config.patch_size
        features = features.view(-1, self.config.cnn_hidden_dim, 1, 1)
        features = features.expand(-1, -1, patch_size, patch_size)
        
        # 反卷积生成预测图像
        predicted_patch = self.deconv_net(features)
        
        return predicted_patch


class AttentionKeyHead(nn.Module):
    """注意力Key头 - 生成候选SPC激活模式"""
    
    def __init__(self, config: SentryNetConfig):
        super().__init__()
        self.config = config
        
        self.key_net = nn.Sequential(
            nn.Linear(config.memory_hidden_dim, config.attention_key_dim),
            nn.ReLU(),
            nn.Dropout(config.dropout_rate),
            nn.Linear(config.attention_key_dim, config.attention_key_dim),
            nn.Tanh()  # 归一化输出
        )
        
    def forward(self, belief_state: torch.Tensor) -> torch.Tensor:
        """
        前向传播
        Args:
            belief_state: [batch_size, memory_hidden_dim] 信念状态
        Returns:
            attention_key: [batch_size, attention_key_dim] 注意力Key向量（候选SPC）
        """
        attention_key = self.key_net(belief_state)
        return attention_key


class MessageValueHead(nn.Module):
    """消息Value头 - 生成注意力机制中的Value向量"""
    
    def __init__(self, config: SentryNetConfig):
        super().__init__()
        self.config = config
        
        self.value_net = nn.Sequential(
            nn.Linear(config.memory_hidden_dim, config.message_value_dim),
            nn.ReLU(),
            nn.Dropout(config.dropout_rate),
            nn.Linear(config.message_value_dim, config.message_value_dim),
            nn.Tanh()  # 归一化输出
        )
        
    def forward(self, belief_state: torch.Tensor) -> torch.Tensor:
        """
        前向传播
        Args:
            belief_state: [batch_size, memory_hidden_dim] 信念状态
        Returns:
            message_value: [batch_size, message_value_dim] 消息Value向量
        """
        message_value = self.value_net(belief_state)
        return message_value


class SentryNet(nn.Module):
    """
    SentryNet主网络 - 社交哨兵网络
    
    通过观察同伴的原始感官信息流，学会判断同伴是否处于关键区域，
    并生成功能性的社交位置细胞(SPC)表征。
    """
    
    def __init__(self, config: SentryNetConfig):
        super().__init__()
        self.config = config
        
        # 核心模块
        self.observation_encoder = ObservationEncoder(config)
        self.input_fusion = InputFusion(config)
        self.memory_module = MemoryModule(config)
        
        # 功能头
        self.predictive_decoder = PredictiveDecoder(config)
        self.attention_key_head = AttentionKeyHead(config)
        self.message_value_head = MessageValueHead(config)
        
    def forward(self, peer_local_patch: torch.Tensor, peer_velocity: torch.Tensor,
                hidden_state: Optional[torch.Tensor] = None) -> Dict[str, torch.Tensor]:
        """
        前向传播
        Args:
            peer_local_patch: [batch_size, num_channels, patch_size, patch_size] 同伴局部地图
            peer_velocity: [batch_size, velocity_dim] 同伴速度向量
            hidden_state: [1, batch_size, memory_hidden_dim] 隐藏状态
        Returns:
            Dict包含:
                - predicted_patch: 预测的下一时刻局部地图
                - attention_key: 注意力Key向量（候选SPC）
                - message_value: 消息Value向量
                - belief_state: 当前信念状态
                - new_hidden_state: 新的隐藏状态
        """
        # 1. 观察编码
        spatial_features = self.observation_encoder(peer_local_patch)
        
        # 2. 输入融合
        fused_features = self.input_fusion(spatial_features, peer_velocity)
        
        # 3. 记忆更新
        belief_state, new_hidden_state = self.memory_module(fused_features, hidden_state)
        
        # 4. 功能头输出
        predicted_patch = self.predictive_decoder(belief_state)
        attention_key = self.attention_key_head(belief_state)
        message_value = self.message_value_head(belief_state)
        
        return {
            'predicted_patch': predicted_patch,
            'attention_key': attention_key,
            'message_value': message_value,
            'belief_state': belief_state,
            'new_hidden_state': new_hidden_state
        }
    
    def reset_hidden_state(self, batch_size: int) -> torch.Tensor:
        """重置隐藏状态"""
        return torch.zeros(1, batch_size, self.config.memory_hidden_dim, 
                          device=self.config.device)


if __name__ == "__main__":
    # 测试SentryNet
    config = SentryNetConfig()
    model = SentryNet(config)
    
    # 创建测试数据
    batch_size = 4
    peer_local_patch = torch.randn(batch_size, config.num_channels, 
                                  config.patch_size, config.patch_size)
    peer_velocity = torch.randn(batch_size, config.velocity_dim)
    
    # 前向传播
    hidden_state = model.reset_hidden_state(batch_size)
    outputs = model(peer_local_patch, peer_velocity, hidden_state)
    
    print("SentryNet测试结果:")
    for key, value in outputs.items():
        if isinstance(value, torch.Tensor):
            print(f"{key}: {value.shape}")
    
    print(f"\n模型参数数量: {sum(p.numel() for p in model.parameters()):,}")
