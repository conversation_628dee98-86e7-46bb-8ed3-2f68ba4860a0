#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SentryNet数据收集器
Data Collector for SentryNet Training

从现有的多智能体SLAM仿真环境中收集同伴观察数据，
生成训练SentryNet所需的序列数据。
"""

import numpy as np
import torch
from typing import List, Dict, Tuple, Optional
from dataclasses import dataclass
import pickle
import os
from collections import deque

from agent_manager import AgentManager, AgentState
from maze_generator import RoomBasedMazeGenerator
from fov_simulator import FOVSimulator
from navigation_strategies import RandomNavigationStrategy, ExplorationNavigationStrategy, BoundaryViolationStrategy


@dataclass
class DataCollectionConfig:
    """数据收集配置"""
    maze_size: int = 15
    num_agents: int = 3
    patch_size: int = 7  # 局部地图切片大小
    velocity_history_steps: int = 5  # 用于计算平均速度的历史步数
    sequence_length: int = 10  # 每个序列的长度
    num_episodes: int = 100  # 收集的episode数量
    steps_per_episode: int = 200  # 每个episode的步数
    save_path: str = "sentry_training_data"

    # 边界违规相关配置
    boundary_threshold: float = 1.5  # 边界阈值
    social_penalty: float = -0.5  # 社会惩罚
    violation_strategy_ratio: float = 0.4  # 使用边界违规策略的智能体比例


class SimpleBoundaryDetector:
    """简单的边界检测器（避免循环导入）"""

    def __init__(self, maze_size: int, boundary_threshold: float):
        self.maze_size = maze_size
        self.boundary_threshold = boundary_threshold

    def is_near_boundary(self, position: np.ndarray, sector_id: int) -> bool:
        """判断位置是否靠近边界"""
        x, y = position

        # 检查迷宫边界
        maze_boundary = (x < self.boundary_threshold or
                        x > self.maze_size - self.boundary_threshold or
                        y < self.boundary_threshold or
                        y > self.maze_size - self.boundary_threshold)

        # 检查扇区边界
        center = self.maze_size // 2
        sector_boundary = False

        if sector_id == 0:  # 左上扇区
            if (abs(x - center) < self.boundary_threshold or
                abs(y - center) < self.boundary_threshold):
                sector_boundary = True
        elif sector_id == 1:  # 右上扇区
            if (abs(x - center) < self.boundary_threshold or
                abs(y - center) < self.boundary_threshold):
                sector_boundary = True
        elif sector_id == 2:  # 左下扇区
            if (abs(x - center) < self.boundary_threshold or
                abs(y - center) < self.boundary_threshold):
                sector_boundary = True

        return maze_boundary or sector_boundary


class PeerObservationExtractor:
    """同伴观察提取器"""
    
    def __init__(self, config: DataCollectionConfig):
        self.config = config
        self.fov_simulator = FOVSimulator(config.maze_size)
        
    def extract_peer_local_patch(self, peer_state: AgentState, 
                                ground_truth_maze: np.ndarray) -> np.ndarray:
        """
        提取同伴的局部地图切片
        Args:
            peer_state: 同伴智能体状态
            ground_truth_maze: 真实地图
        Returns:
            local_patch: [num_channels, patch_size, patch_size] 局部地图切片
        """
        # 获取同伴当前的FOV观察
        peer_fov = self.fov_simulator.get_fov_observation(
            peer_state.position, peer_state.direction, ground_truth_maze
        )
        
        # 提取以同伴位置为中心的局部切片
        center_x, center_y = int(peer_state.position[0]), int(peer_state.position[1])
        half_patch = self.config.patch_size // 2
        
        # 计算切片边界
        x_start = max(0, center_x - half_patch)
        x_end = min(self.config.maze_size, center_x + half_patch + 1)
        y_start = max(0, center_y - half_patch)
        y_end = min(self.config.maze_size, center_y + half_patch + 1)
        
        # 创建局部切片
        local_patch = np.full((self.config.patch_size, self.config.patch_size), -1, dtype=np.float32)
        
        # 填充有效区域
        patch_x_start = half_patch - (center_x - x_start)
        patch_x_end = patch_x_start + (x_end - x_start)
        patch_y_start = half_patch - (center_y - y_start)
        patch_y_end = patch_y_start + (y_end - y_start)
        
        local_patch[patch_y_start:patch_y_end, patch_x_start:patch_x_end] = \
            peer_fov[y_start:y_end, x_start:x_end]
        
        # 转换为多通道格式
        # 通道0: 墙壁/通路 (0/1)，未知区域用0.5表示
        # 通道1: 未知区域掩码 (1表示未知，0表示已知)
        channel_0 = np.where(local_patch == -1, 0.5, local_patch)
        channel_1 = (local_patch == -1).astype(np.float32)
        
        multi_channel_patch = np.stack([channel_0, channel_1], axis=0)
        
        return multi_channel_patch
    
    def calculate_peer_velocity(self, position_history: deque) -> np.ndarray:
        """
        计算同伴的平均速度
        Args:
            position_history: 位置历史队列
        Returns:
            velocity: [2] 平均速度向量 (vx, vy)
        """
        if len(position_history) < 2:
            return np.zeros(2, dtype=np.float32)
        
        # 计算最近几步的平均速度
        positions = list(position_history)
        velocities = []
        
        for i in range(1, len(positions)):
            vel = positions[i] - positions[i-1]
            velocities.append(vel)
        
        if velocities:
            avg_velocity = np.mean(velocities, axis=0)
        else:
            avg_velocity = np.zeros(2)
        
        return avg_velocity.astype(np.float32)


class SentryDataCollector:
    """SentryNet数据收集器主类"""
    
    def __init__(self, config: DataCollectionConfig):
        self.config = config
        self.extractor = PeerObservationExtractor(config)
        self.maze_generator = RoomBasedMazeGenerator(config.maze_size)
        
        # 导航策略（包含边界违规策略）
        self.navigation_strategies = [
            RandomNavigationStrategy(),
            ExplorationNavigationStrategy(),
            BoundaryViolationStrategy(violation_probability=0.5)  # 50%概率违规
        ]

        # 边界检测器
        self.boundary_detector = SimpleBoundaryDetector(config.maze_size, config.boundary_threshold)
        
        # 数据存储
        self.collected_sequences = []
        
    def collect_episode_data(self, episode_id: int) -> List[Dict]:
        """
        收集单个episode的数据
        Args:
            episode_id: episode编号
        Returns:
            episode_sequences: 该episode收集的序列数据
        """
        print(f"收集Episode {episode_id + 1}/{self.config.num_episodes}")
        
        # 生成新的迷宫
        maze = self.maze_generator.generate_room_based_maze()
        
        # 初始化智能体
        agent_manager = AgentManager(self.config.maze_size, self.config.num_agents)
        agent_manager.initialize_agents(maze)
        
        # 为每个智能体分配导航策略（确保有边界违规行为）
        strategies = {}
        num_violation_agents = max(1, int(self.config.num_agents * self.config.violation_strategy_ratio))

        for i in range(self.config.num_agents):
            if i < num_violation_agents:
                # 前几个智能体使用边界违规策略
                strategies[i] = BoundaryViolationStrategy(violation_probability=0.6)
            else:
                # 其他智能体使用正常策略
                strategies[i] = np.random.choice(self.navigation_strategies[:-1])  # 排除边界违规策略
        
        # 位置历史记录
        position_histories = {i: deque(maxlen=self.config.velocity_history_steps) 
                             for i in range(self.config.num_agents)}
        
        # 序列数据缓存
        sequence_buffers = {i: [] for i in range(self.config.num_agents)}
        episode_sequences = []

        # 边界违规统计
        boundary_violations = {i: 0 for i in range(self.config.num_agents)}
        social_penalties = {i: 0.0 for i in range(self.config.num_agents)}
        
        # 仿真步骤
        for step in range(self.config.steps_per_episode):
            # 更新每个智能体
            for agent_id, agent_state in enumerate(agent_manager.agent_states):
                # 记录当前位置
                position_histories[agent_id].append(agent_state.position.copy())

                # 检查边界违规
                is_boundary_violation = self.boundary_detector.is_near_boundary(
                    agent_state.position, agent_state.sector
                )

                if is_boundary_violation:
                    boundary_violations[agent_id] += 1
                    social_penalties[agent_id] += self.config.social_penalty

                # 提取当前观察数据
                current_patch = self.extractor.extract_peer_local_patch(agent_state, maze)
                current_velocity = self.extractor.calculate_peer_velocity(position_histories[agent_id])

                # 存储到序列缓存（包含边界违规信息）
                sequence_buffers[agent_id].append({
                    'patch': current_patch,
                    'velocity': current_velocity,
                    'position': agent_state.position.copy(),
                    'step': step,
                    'is_boundary_violation': is_boundary_violation,
                    'social_penalty': self.config.social_penalty if is_boundary_violation else 0.0
                })
                
                # 移动智能体
                new_pos, new_dir = strategies[agent_id].select_next_move(agent_state, maze)
                agent_state.position = new_pos
                agent_state.direction = new_dir
                agent_state.step_count += 1
                
                # 更新FOV观察
                agent_state.local_map = self.extractor.fov_simulator.get_fov_observation(
                    agent_state.position, agent_state.direction, maze
                )
            
            # 每隔一定步数生成训练序列
            if step >= self.config.sequence_length and step % 5 == 0:
                for agent_id in range(self.config.num_agents):
                    sequence = self._create_training_sequence(
                        sequence_buffers[agent_id], agent_id, episode_id
                    )
                    if sequence:
                        episode_sequences.append(sequence)

        # 打印边界违规统计
        total_violations = sum(boundary_violations.values())
        total_penalties = sum(social_penalties.values())
        print(f"Episode {episode_id + 1} 边界违规统计:")
        print(f"  总违规次数: {total_violations}")
        print(f"  总社会惩罚: {total_penalties:.1f}")
        for agent_id in range(self.config.num_agents):
            print(f"  智能体{agent_id}: {boundary_violations[agent_id]}次违规, 惩罚{social_penalties[agent_id]:.1f}")

        return episode_sequences
    
    def _create_training_sequence(self, buffer: List[Dict], agent_id: int, episode_id: int) -> Optional[Dict]:
        """
        从缓存中创建训练序列
        Args:
            buffer: 观察数据缓存
            agent_id: 智能体ID
            episode_id: episode ID
        Returns:
            training_sequence: 训练序列数据
        """
        if len(buffer) < self.config.sequence_length + 1:
            return None
        
        # 提取序列
        sequence_data = buffer[-self.config.sequence_length-1:]
        
        # 构建输入-输出对
        inputs = []
        targets = []
        
        for i in range(len(sequence_data) - 1):
            current_data = sequence_data[i]
            next_data = sequence_data[i + 1]
            
            inputs.append({
                'patch': current_data['patch'],
                'velocity': current_data['velocity'],
                'is_boundary_violation': current_data.get('is_boundary_violation', False),
                'social_penalty': current_data.get('social_penalty', 0.0)
            })
            targets.append({
                'patch': next_data['patch'],
                'is_boundary_violation': next_data.get('is_boundary_violation', False)
            })
        
        return {
            'agent_id': agent_id,
            'episode_id': episode_id,
            'inputs': inputs,
            'targets': targets,
            'metadata': {
                'sequence_length': len(inputs),
                'start_step': sequence_data[0]['step'],
                'end_step': sequence_data[-1]['step']
            }
        }
    
    def collect_all_data(self) -> None:
        """收集所有训练数据"""
        print(f"开始收集SentryNet训练数据...")
        print(f"配置: {self.config.num_episodes} episodes, {self.config.steps_per_episode} steps/episode")
        
        all_sequences = []
        
        for episode_id in range(self.config.num_episodes):
            episode_sequences = self.collect_episode_data(episode_id)
            all_sequences.extend(episode_sequences)
            
            if (episode_id + 1) % 10 == 0:
                print(f"已收集 {len(all_sequences)} 个训练序列")
        
        self.collected_sequences = all_sequences
        print(f"数据收集完成！总共收集了 {len(all_sequences)} 个训练序列")
    
    def save_data(self) -> None:
        """保存收集的数据"""
        os.makedirs(self.config.save_path, exist_ok=True)
        
        # 保存原始序列数据
        data_file = os.path.join(self.config.save_path, "training_sequences.pkl")
        with open(data_file, 'wb') as f:
            pickle.dump(self.collected_sequences, f)
        
        # 保存配置
        config_file = os.path.join(self.config.save_path, "collection_config.pkl")
        with open(config_file, 'wb') as f:
            pickle.dump(self.config, f)
        
        print(f"数据已保存到 {self.config.save_path}")
        print(f"- 训练序列: {data_file}")
        print(f"- 配置文件: {config_file}")
    
    def load_data(self) -> List[Dict]:
        """加载已保存的数据"""
        data_file = os.path.join(self.config.save_path, "training_sequences.pkl")
        
        if not os.path.exists(data_file):
            raise FileNotFoundError(f"数据文件不存在: {data_file}")
        
        with open(data_file, 'rb') as f:
            sequences = pickle.load(f)
        
        print(f"从 {data_file} 加载了 {len(sequences)} 个训练序列")
        return sequences
    
    def get_data_statistics(self) -> Dict:
        """获取数据统计信息"""
        if not self.collected_sequences:
            return {}
        
        total_sequences = len(self.collected_sequences)
        total_samples = sum(len(seq['inputs']) for seq in self.collected_sequences)
        
        # 按智能体统计
        agent_stats = {}
        for seq in self.collected_sequences:
            agent_id = seq['agent_id']
            if agent_id not in agent_stats:
                agent_stats[agent_id] = 0
            agent_stats[agent_id] += len(seq['inputs'])
        
        return {
            'total_sequences': total_sequences,
            'total_samples': total_samples,
            'samples_per_agent': agent_stats,
            'avg_sequence_length': total_samples / total_sequences if total_sequences > 0 else 0
        }


if __name__ == "__main__":
    # 测试数据收集器
    config = DataCollectionConfig(
        num_episodes=5,  # 测试用少量episode
        steps_per_episode=50
    )
    
    collector = SentryDataCollector(config)
    
    # 收集数据
    collector.collect_all_data()
    
    # 显示统计信息
    stats = collector.get_data_statistics()
    print("\n数据统计:")
    for key, value in stats.items():
        print(f"{key}: {value}")
    
    # 保存数据
    collector.save_data()
