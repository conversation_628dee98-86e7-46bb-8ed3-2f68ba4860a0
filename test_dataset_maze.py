#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据集迷宫加载测试脚本
Dataset Maze Loading Test Script
"""

import numpy as np
import matplotlib.pyplot as plt
from maze_generator import RoomBasedMazeGenerator
from main_validator import MultiAgentSLAMValidator
from navigation_strategies import ExplorationNavigationStrategy


def test_dataset_maze_loading():
    """测试数据集迷宫加载功能"""
    print("=" * 60)
    print("测试数据集迷宫加载功能")
    print("=" * 60)
    
    # 创建迷宫生成器
    generator = RoomBasedMazeGenerator(maze_size=15, seed=42)
    
    # 测试加载5个随机数据集迷宫
    for i in range(5):
        print(f"\n🎯 加载第 {i+1} 个随机数据集迷宫")
        
        try:
            # 加载随机数据集迷宫
            maze, config = generator.load_random_dataset_maze()
            
            # 打印迷宫信息
            print(f"迷宫尺寸: {maze.shape}")
            print(f"通路占比: {np.sum(maze == 1) / maze.size:.2%}")
            
            if config:
                print(f"环境名称: {config.get('env_name', 'Unknown')}")
                print(f"种子: {config.get('seed', 'Unknown')}")
                if 'objects' in config:
                    print(f"对象数量: {len(config['objects'])}")
            
            # 可视化迷宫
            plt.figure(figsize=(8, 8))
            plt.imshow(maze, cmap='gray', origin='lower')
            plt.title(f'Dataset Maze {i+1}')
            plt.grid(True, alpha=0.3)
            plt.colorbar(label='0=Wall, 1=Path')
            
            # 保存图像
            save_path = f"dataset_maze_{i+1}.png"
            plt.savefig(save_path, dpi=150, bbox_inches='tight')
            plt.close()
            print(f"迷宫图像已保存: {save_path}")
            
        except Exception as e:
            print(f"加载迷宫 {i+1} 时出错: {e}")
    
    print(f"\n✅ 数据集迷宫加载测试完成!")


def test_dataset_simulation():
    """测试使用数据集迷宫进行仿真"""
    print("=" * 60)
    print("测试数据集迷宫仿真")
    print("=" * 60)
    
    # 创建使用数据集迷宫的验证器
    validator = MultiAgentSLAMValidator(
        maze_size=15, 
        num_agents=3, 
        seed=42, 
        use_dataset_maze=True
    )
    
    # 使用探索策略
    strategy = ExplorationNavigationStrategy()
    
    # 初始化仿真
    print("初始化仿真...")
    validator.initialize_simulation(strategy, maze_complexity=0.7)
    
    # 运行短时间仿真
    print("运行仿真...")
    results = validator.run_simulation(max_steps=100, record_video=True)
    
    # 可视化结果
    save_path = "dataset_simulation_results.png"
    validator.visualize_results(save_path)
    
    # 导出结果
    json_path = "dataset_simulation_results.json"
    validator.export_results(json_path)
    
    # 打印统计信息
    print(f"\n📊 仿真结果统计:")
    print(f"   总奖励: {sum(state.reward_score for state in validator.agent_manager.agent_states):.2f}")
    print(f"   探索进度: {results['exploration_progress'][-1]:.3f}")
    if results['correlation_history']:
        final_corr = results['correlation_history'][-1]['mean_correlation']
        print(f"   最终平均相关性: {final_corr:.3f}")
    print(f"   区域违规总数: {sum(results['sector_violations'])}")
    
    print(f"\n✅ 数据集迷宫仿真测试完成!")
    print(f"结果已保存到: {save_path} 和 {json_path}")


def compare_generated_vs_dataset():
    """比较生成迷宫与数据集迷宫的性能"""
    print("=" * 60)
    print("比较生成迷宫与数据集迷宫")
    print("=" * 60)
    
    results_comparison = []
    
    # 测试生成迷宫
    print("\n🔧 测试生成迷宫...")
    validator_generated = MultiAgentSLAMValidator(
        maze_size=15, 
        num_agents=3, 
        seed=42, 
        use_dataset_maze=False
    )
    
    strategy = ExplorationNavigationStrategy()
    validator_generated.initialize_simulation(strategy, maze_complexity=0.7)
    results_gen = validator_generated.run_simulation(max_steps=100, record_video=False)
    
    gen_stats = {
        'type': 'Generated',
        'total_reward': sum(state.reward_score for state in validator_generated.agent_manager.agent_states),
        'exploration_progress': results_gen['exploration_progress'][-1],
        'final_correlation': results_gen['correlation_history'][-1]['mean_correlation'] if results_gen['correlation_history'] else 0,
        'sector_violations': sum(results_gen['sector_violations'])
    }
    results_comparison.append(gen_stats)
    
    # 测试数据集迷宫
    print("\n📊 测试数据集迷宫...")
    validator_dataset = MultiAgentSLAMValidator(
        maze_size=15, 
        num_agents=3, 
        seed=42, 
        use_dataset_maze=True
    )
    
    validator_dataset.initialize_simulation(strategy, maze_complexity=0.7)
    results_dataset = validator_dataset.run_simulation(max_steps=100, record_video=False)
    
    dataset_stats = {
        'type': 'Dataset',
        'total_reward': sum(state.reward_score for state in validator_dataset.agent_manager.agent_states),
        'exploration_progress': results_dataset['exploration_progress'][-1],
        'final_correlation': results_dataset['correlation_history'][-1]['mean_correlation'] if results_dataset['correlation_history'] else 0,
        'sector_violations': sum(results_dataset['sector_violations'])
    }
    results_comparison.append(dataset_stats)
    
    # 打印比较结果
    print(f"\n📈 性能比较结果:")
    print(f"{'类型':<12} {'总奖励':<12} {'探索进度':<12} {'相关性':<12} {'违规次数':<12}")
    print("-" * 60)
    
    for stats in results_comparison:
        print(f"{stats['type']:<12} {stats['total_reward']:<12.1f} {stats['exploration_progress']:<12.3f} "
              f"{stats['final_correlation']:<12.3f} {stats['sector_violations']:<12}")
    
    print(f"\n✅ 比较测试完成!")


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1:
        if sys.argv[1] == "load":
            test_dataset_maze_loading()
        elif sys.argv[1] == "sim":
            test_dataset_simulation()
        elif sys.argv[1] == "compare":
            compare_generated_vs_dataset()
        else:
            print("用法: python test_dataset_maze.py [load|sim|compare]")
            print("  load    - 测试数据集迷宫加载")
            print("  sim     - 测试数据集迷宫仿真")
            print("  compare - 比较生成迷宫与数据集迷宫")
    else:
        # 默认运行所有测试
        test_dataset_maze_loading()
        test_dataset_simulation()
        compare_generated_vs_dataset()
