#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SentryNet训练器
SentryNet Trainer for Self-Supervised Learning

使用自监督学习训练SentryNet，优化预测损失函数，
使网络学会预测同伴的未来局部地图。
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
import numpy as np
from typing import List, Dict, Tuple, Optional
import os
import pickle
from tqdm import tqdm
import matplotlib.pyplot as plt
from dataclasses import dataclass

from sentry_net import SentryNet, SentryNetConfig
from sentry_data_collector import SentryDataCollector, DataCollectionConfig


@dataclass
class TrainingConfig:
    """训练配置"""
    # 数据配置
    data_path: str = "sentry_training_data"
    batch_size: int = 32
    sequence_length: int = 10
    train_split: float = 0.8
    
    # 训练配置
    num_epochs: int = 100
    learning_rate: float = 1e-3
    weight_decay: float = 1e-4
    gradient_clip_norm: float = 1.0
    
    # 损失权重
    prediction_loss_weight: float = 1.0
    
    # 保存配置
    save_dir: str = "sentry_models"
    save_interval: int = 10
    
    # 设备配置
    device: str = "cuda" if torch.cuda.is_available() else "cpu"


class SentryDataset(Dataset):
    """SentryNet训练数据集"""
    
    def __init__(self, sequences: List[Dict], config: TrainingConfig):
        self.sequences = sequences
        self.config = config
        
        # 展开所有样本
        self.samples = []
        for seq in sequences:
            for i in range(len(seq['inputs'])):
                self.samples.append({
                    'input_patch': seq['inputs'][i]['patch'],
                    'input_velocity': seq['inputs'][i]['velocity'],
                    'target_patch': seq['targets'][i],
                    'agent_id': seq['agent_id'],
                    'episode_id': seq['episode_id']
                })
    
    def __len__(self) -> int:
        return len(self.samples)
    
    def __getitem__(self, idx: int) -> Dict[str, torch.Tensor]:
        sample = self.samples[idx]
        
        return {
            'input_patch': torch.FloatTensor(sample['input_patch']),
            'input_velocity': torch.FloatTensor(sample['input_velocity']),
            'target_patch': torch.FloatTensor(sample['target_patch']),
            'agent_id': torch.LongTensor([sample['agent_id']]),
            'episode_id': torch.LongTensor([sample['episode_id']])
        }


class SentryTrainer:
    """SentryNet训练器"""
    
    def __init__(self, sentry_config: SentryNetConfig, training_config: TrainingConfig):
        self.sentry_config = sentry_config
        self.training_config = training_config
        
        # 创建模型
        self.model = SentryNet(sentry_config).to(training_config.device)
        
        # 优化器
        self.optimizer = optim.Adam(
            self.model.parameters(),
            lr=training_config.learning_rate,
            weight_decay=training_config.weight_decay
        )
        
        # 学习率调度器
        self.scheduler = optim.lr_scheduler.ReduceLROnPlateau(
            self.optimizer, mode='min', factor=0.5, patience=10, verbose=True
        )
        
        # 损失函数
        self.prediction_loss_fn = nn.BCELoss()
        
        # 训练历史
        self.train_losses = []
        self.val_losses = []
        
        # 创建保存目录
        os.makedirs(training_config.save_dir, exist_ok=True)
    
    def load_data(self) -> Tuple[DataLoader, DataLoader]:
        """加载和准备训练数据"""
        print("加载训练数据...")
        
        # 加载数据
        data_file = os.path.join(self.training_config.data_path, "training_sequences.pkl")
        with open(data_file, 'rb') as f:
            sequences = pickle.load(f)
        
        print(f"加载了 {len(sequences)} 个序列")
        
        # 划分训练集和验证集
        split_idx = int(len(sequences) * self.training_config.train_split)
        train_sequences = sequences[:split_idx]
        val_sequences = sequences[split_idx:]
        
        print(f"训练集: {len(train_sequences)} 序列")
        print(f"验证集: {len(val_sequences)} 序列")
        
        # 创建数据集
        train_dataset = SentryDataset(train_sequences, self.training_config)
        val_dataset = SentryDataset(val_sequences, self.training_config)
        
        print(f"训练样本: {len(train_dataset)}")
        print(f"验证样本: {len(val_dataset)}")
        
        # 创建数据加载器
        train_loader = DataLoader(
            train_dataset,
            batch_size=self.training_config.batch_size,
            shuffle=True,
            num_workers=4,
            pin_memory=True
        )
        
        val_loader = DataLoader(
            val_dataset,
            batch_size=self.training_config.batch_size,
            shuffle=False,
            num_workers=4,
            pin_memory=True
        )
        
        return train_loader, val_loader
    
    def train_epoch(self, train_loader: DataLoader) -> float:
        """训练一个epoch"""
        self.model.train()
        total_loss = 0.0
        num_batches = 0
        
        progress_bar = tqdm(train_loader, desc="Training")
        
        for batch in progress_bar:
            # 移动数据到设备
            input_patch = batch['input_patch'].to(self.training_config.device)
            input_velocity = batch['input_velocity'].to(self.training_config.device)
            target_patch = batch['target_patch'].to(self.training_config.device)
            
            # 重置梯度
            self.optimizer.zero_grad()
            
            # 前向传播
            batch_size = input_patch.size(0)
            hidden_state = self.model.reset_hidden_state(batch_size)
            
            outputs = self.model(input_patch, input_velocity, hidden_state)
            predicted_patch = outputs['predicted_patch']
            
            # 计算损失
            prediction_loss = self.prediction_loss_fn(predicted_patch, target_patch)
            total_loss_batch = prediction_loss * self.training_config.prediction_loss_weight
            
            # 反向传播
            total_loss_batch.backward()
            
            # 梯度裁剪
            torch.nn.utils.clip_grad_norm_(
                self.model.parameters(), 
                self.training_config.gradient_clip_norm
            )
            
            # 更新参数
            self.optimizer.step()
            
            # 统计
            total_loss += total_loss_batch.item()
            num_batches += 1
            
            # 更新进度条
            progress_bar.set_postfix({
                'Loss': f'{total_loss_batch.item():.4f}',
                'Avg Loss': f'{total_loss/num_batches:.4f}'
            })
        
        return total_loss / num_batches
    
    def validate_epoch(self, val_loader: DataLoader) -> float:
        """验证一个epoch"""
        self.model.eval()
        total_loss = 0.0
        num_batches = 0
        
        with torch.no_grad():
            for batch in tqdm(val_loader, desc="Validation"):
                # 移动数据到设备
                input_patch = batch['input_patch'].to(self.training_config.device)
                input_velocity = batch['input_velocity'].to(self.training_config.device)
                target_patch = batch['target_patch'].to(self.training_config.device)
                
                # 前向传播
                batch_size = input_patch.size(0)
                hidden_state = self.model.reset_hidden_state(batch_size)
                
                outputs = self.model(input_patch, input_velocity, hidden_state)
                predicted_patch = outputs['predicted_patch']
                
                # 计算损失
                prediction_loss = self.prediction_loss_fn(predicted_patch, target_patch)
                total_loss_batch = prediction_loss * self.training_config.prediction_loss_weight
                
                total_loss += total_loss_batch.item()
                num_batches += 1
        
        return total_loss / num_batches
    
    def train(self) -> None:
        """完整训练流程"""
        print("开始训练SentryNet...")
        print(f"设备: {self.training_config.device}")
        print(f"模型参数数量: {sum(p.numel() for p in self.model.parameters()):,}")
        
        # 加载数据
        train_loader, val_loader = self.load_data()
        
        best_val_loss = float('inf')
        
        for epoch in range(self.training_config.num_epochs):
            print(f"\nEpoch {epoch + 1}/{self.training_config.num_epochs}")
            
            # 训练
            train_loss = self.train_epoch(train_loader)
            self.train_losses.append(train_loss)
            
            # 验证
            val_loss = self.validate_epoch(val_loader)
            self.val_losses.append(val_loss)
            
            # 学习率调度
            self.scheduler.step(val_loss)
            
            print(f"Train Loss: {train_loss:.4f}, Val Loss: {val_loss:.4f}")
            
            # 保存最佳模型
            if val_loss < best_val_loss:
                best_val_loss = val_loss
                self.save_model("best_model.pth")
                print(f"保存最佳模型 (Val Loss: {val_loss:.4f})")
            
            # 定期保存
            if (epoch + 1) % self.training_config.save_interval == 0:
                self.save_model(f"model_epoch_{epoch + 1}.pth")
        
        print("训练完成！")
        self.plot_training_curves()
    
    def save_model(self, filename: str) -> None:
        """保存模型"""
        filepath = os.path.join(self.training_config.save_dir, filename)
        torch.save({
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'sentry_config': self.sentry_config,
            'training_config': self.training_config,
            'train_losses': self.train_losses,
            'val_losses': self.val_losses
        }, filepath)
    
    def load_model(self, filename: str) -> None:
        """加载模型"""
        filepath = os.path.join(self.training_config.save_dir, filename)
        checkpoint = torch.load(filepath, map_location=self.training_config.device)
        
        self.model.load_state_dict(checkpoint['model_state_dict'])
        self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
        self.train_losses = checkpoint.get('train_losses', [])
        self.val_losses = checkpoint.get('val_losses', [])
        
        print(f"模型已从 {filepath} 加载")
    
    def plot_training_curves(self) -> None:
        """绘制训练曲线"""
        if not self.train_losses:
            return
        
        plt.figure(figsize=(10, 6))
        epochs = range(1, len(self.train_losses) + 1)
        
        plt.plot(epochs, self.train_losses, 'b-', label='Training Loss')
        plt.plot(epochs, self.val_losses, 'r-', label='Validation Loss')
        
        plt.title('SentryNet Training Curves')
        plt.xlabel('Epoch')
        plt.ylabel('Loss')
        plt.legend()
        plt.grid(True)
        
        # 保存图像
        plt.savefig(os.path.join(self.training_config.save_dir, 'training_curves.png'))
        plt.show()


def main():
    """主函数"""
    # 配置
    sentry_config = SentryNetConfig()
    training_config = TrainingConfig()
    
    # 检查数据是否存在
    data_file = os.path.join(training_config.data_path, "training_sequences.pkl")
    if not os.path.exists(data_file):
        print("训练数据不存在，开始收集数据...")
        
        # 收集数据
        collection_config = DataCollectionConfig()
        collector = SentryDataCollector(collection_config)
        collector.collect_all_data()
        collector.save_data()
    
    # 创建训练器
    trainer = SentryTrainer(sentry_config, training_config)
    
    # 开始训练
    trainer.train()


if __name__ == "__main__":
    main()
