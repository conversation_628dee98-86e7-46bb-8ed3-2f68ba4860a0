# -*- coding: utf-8 -*-
# File: video_recorder.py
#!/usr/bin/env python3
"""
视频录制器 - 录制多智能体运动过程的视频
Video Recorder - Records video of multi-agent movement process
"""

import numpy as np
import matplotlib.pyplot as plt
import matplotlib.animation as animation
from matplotlib.patches import Wedge, Circle
import math
from typing import List, Optional, Tuple
from agent_manager import AgentState


class VideoRecorder:
    """视频录制器"""
    
    def __init__(self, maze_size: int, figsize: Tuple[int, int] = (12, 8)):
        self.maze_size = maze_size
        self.figsize = figsize
        self.frames = []
        self.fig = None
        self.axes = None
        
        # 颜色配置
        self.agent_colors = ['red', 'blue', 'green', 'orange', 'purple']
        self.sector_colors = [(1,0,0,0.3), (0,1,0,0.3), (0,0,1,0.3), (1,1,0,0.3), (1,0,1,0.3)]
    
    def initialize_recording(self):
        """初始化录制"""
        self.frames = []
        print("视频录制器已初始化")
    
    def add_frame(self, ground_truth_maze: np.ndarray, agent_states: List[AgentState], 
                  sectors: List[np.ndarray], step: int):
        """
        添加一帧到视频
        Args:
            ground_truth_maze: 真实迷宫
            agent_states: 智能体状态列表
            sectors: 扇区列表
            step: 当前步数
        """
        frame_data = {
            'step': step,
            'ground_truth_maze': ground_truth_maze.copy(),
            'agent_states': [self._copy_agent_state(state) for state in agent_states],
            'sectors': [sector.copy() for sector in sectors]
        }
        self.frames.append(frame_data)
    
    def _copy_agent_state(self, agent_state: AgentState) -> dict:
        """复制智能体状态（避免引用问题）"""
        return {
            'agent_id': agent_state.agent_id,
            'position': agent_state.position.copy(),
            'direction': agent_state.direction.copy(),
            'sector': agent_state.sector,
            'local_map': agent_state.local_map.copy(),
            'slam_map': agent_state.slam_map.copy(),  # 添加SLAM地图
            'exploration_area': agent_state.exploration_area,
            'reward_score': agent_state.reward_score,
            'step_count': agent_state.step_count
        }
    
    def save_video(self, filename: str, fps: int = 5, dpi: int = 100):
        """
        保存视频文件
        Args:
            filename: 输出文件名
            fps: 帧率
            dpi: 分辨率
        """
        if not self.frames:
            print("没有帧数据，无法生成视频")
            return
        
        print(f"开始生成视频，共{len(self.frames)}帧...")
        
        # 创建图形
        self.fig, self.axes = plt.subplots(2, 2, figsize=self.figsize)
        self.fig.suptitle('Multi-Agent SLAM Simulation', fontsize=16)
        
        # 创建动画
        anim = animation.FuncAnimation(
            self.fig, self._animate_frame, frames=len(self.frames),
            interval=1000//fps, blit=False, repeat=True
        )
        
        # 尝试多种保存方式
        success = False
        
        # 1. 首先尝试保存为GIF（兼容性最好）
        try:
            gif_filename = filename.replace('.mp4', '.gif').replace('.avi', '.gif')
            anim.save(gif_filename, writer='pillow', fps=fps, dpi=dpi//2)
            print(f"✅ GIF视频已保存: {gif_filename}")
            success = True
        except Exception as e:
            print(f"保存GIF失败: {e}")
        
        # 2. 尝试使用matplotlib默认writer
        if not success:
            try:
                # 检查可用的writer
                available_writers = animation.writers.list()
                print(f"可用的视频编码器: {available_writers}")
                
                if 'ffmpeg' in available_writers:
                    # 尝试使用更兼容的ffmpeg参数
                    writer = animation.FFMpegWriter(
                        fps=fps, 
                        metadata=dict(artist='SLAM Validator'),
                        codec='mpeg4',  # 使用更兼容的编码器
                        bitrate=1800
                    )
                    anim.save(filename, writer=writer, dpi=dpi)
                    print(f"✅ MP4视频已保存: {filename}")
                    success = True
                    
            except Exception as e:
                print(f"使用ffmpeg保存失败: {e}")
        
        # 3. 尝试使用imagemagick
        if not success:
            try:
                if 'imagemagick' in animation.writers.list():
                    writer = animation.ImageMagickWriter(fps=fps)
                    gif_filename = filename.replace('.mp4', '.gif').replace('.avi', '.gif')
                    anim.save(gif_filename, writer=writer, dpi=dpi//2)
                    print(f"✅ ImageMagick GIF已保存: {gif_filename}")
                    success = True
            except Exception as e:
                print(f"使用ImageMagick保存失败: {e}")
        
        # 4. 最后尝试保存静态图片序列
        if not success:
            try:
                self._save_frame_sequence(filename, fps, dpi)
                success = True
            except Exception as e:
                print(f"保存图片序列也失败: {e}")
        
        if not success:
            print("❌ 所有视频保存方式都失败了")
            print("💡 建议:")
            print("   1. 安装ffmpeg: conda install ffmpeg 或 pip install ffmpeg-python")
            print("   2. 安装ImageMagick: conda install imagemagick")
            print("   3. 或者只查看静态结果图片")
        
        plt.close(self.fig)
    
    def _save_frame_sequence(self, base_filename: str, fps: int, dpi: int):
        """保存为图片序列作为备选方案"""
        import os
        
        # 创建输出目录
        base_name = os.path.splitext(base_filename)[0]
        output_dir = f"{base_name}_frames"
        os.makedirs(output_dir, exist_ok=True)
        
        print(f"保存为图片序列到目录: {output_dir}")
        
        # 保存每一帧
        for frame_idx in range(min(10, len(self.frames))):  # 只保存前10帧作为示例
            # 清空所有子图
            for ax in self.axes.flat:
                ax.clear()
            
            # 绘制当前帧
            self._animate_frame(frame_idx)
            
            # 保存图片
            frame_filename = os.path.join(output_dir, f"frame_{frame_idx:03d}.png")
            self.fig.savefig(frame_filename, dpi=dpi//2, bbox_inches='tight')
        
        print(f"✅ 已保存{min(10, len(self.frames))}帧图片到 {output_dir}")
        print(f"   可以使用以下命令合成视频:")
        print(f"   ffmpeg -r {fps} -i {output_dir}/frame_%03d.png -c:v libx264 -pix_fmt yuv420p {base_filename}")
        
        # 另外保存一个总览图
        try:
            self._create_summary_image(f"{base_name}_summary.png", dpi)
        except Exception as e:
            print(f"创建总览图失败: {e}")
    
    def _create_summary_image(self, filename: str, dpi: int):
        """创建仿真总览图"""
        if len(self.frames) < 4:
            return
        
        # 选择几个关键帧
        key_frames = [0, len(self.frames)//3, 2*len(self.frames)//3, len(self.frames)-1]
        
        fig, axes = plt.subplots(2, 2, figsize=(12, 10))
        fig.suptitle('Multi-Agent SLAM Simulation Summary', fontsize=16)
        
        for idx, frame_idx in enumerate(key_frames):
            ax = axes[idx // 2, idx % 2]
            frame_data = self.frames[frame_idx]
            
            # 绘制迷宫和智能体轨迹
            ax.imshow(frame_data['ground_truth_maze'], cmap='gray', origin='lower', alpha=0.7)
            
            # 绘制到当前帧的所有轨迹
            colors = ['red', 'blue', 'green', 'orange', 'purple']
            for agent_idx in range(len(frame_data['agent_states'])):
                trajectory_x = []
                trajectory_y = []
                
                # 收集到当前帧的轨迹
                for past_frame_idx in range(frame_idx + 1):
                    if past_frame_idx < len(self.frames):
                        past_states = self.frames[past_frame_idx]['agent_states']
                        if agent_idx < len(past_states):
                            trajectory_x.append(past_states[agent_idx]['position'][0])
                            trajectory_y.append(past_states[agent_idx]['position'][1])
                
                if len(trajectory_x) > 1:
                    color = colors[agent_idx % len(colors)]
                    ax.plot(trajectory_x, trajectory_y, '-', color=color, alpha=0.8, linewidth=2)
                    ax.plot(trajectory_x[-1], trajectory_y[-1], 'o', color=color, markersize=8)
            
            ax.set_title(f'Step {frame_data["step"]}')
            ax.set_xlim(-0.5, self.maze_size - 0.5)
            ax.set_ylim(-0.5, self.maze_size - 0.5)
        
        plt.tight_layout()
        plt.savefig(filename, dpi=dpi, bbox_inches='tight')
        plt.close()
        print(f"✅ 仿真总览图已保存: {filename}")
    
    def _animate_frame(self, frame_idx: int):
        """动画帧函数"""
        # 清空所有子图
        for ax in self.axes.flat:
            ax.clear()
        
        frame_data = self.frames[frame_idx]
        step = frame_data['step']
        ground_truth_maze = frame_data['ground_truth_maze']
        agent_states = frame_data['agent_states']
        sectors = frame_data['sectors']
        
        # 1. 真实迷宫 + 智能体位置和轨迹
        self._plot_maze_with_agents(self.axes[0, 0], ground_truth_maze, agent_states, frame_idx, step)
        
        # 2. 扇区分割
        self._plot_sectors(self.axes[0, 1], sectors, agent_states)
        
        # 3. 智能体观察地图（融合地图）
        self._plot_agent_observations(self.axes[1, 0], agent_states)

        # 4. 单个智能体SLAM地图对比
        self._plot_individual_slam_maps(self.axes[1, 1], agent_states, step)
        
        plt.tight_layout()
        return self.axes.flat
    
    def _plot_maze_with_agents(self, ax, ground_truth_maze, agent_states, frame_idx, step):
        """绘制迷宫和智能体"""
        # 显示迷宫
        ax.imshow(ground_truth_maze, cmap='gray', origin='lower', alpha=0.7)
        
        # 绘制智能体轨迹
        if frame_idx > 0:
            for agent_idx, agent_state in enumerate(agent_states):
                agent_id = agent_state['agent_id']
                trajectory_x = []
                trajectory_y = []
                
                # 收集历史轨迹
                for past_frame_idx in range(max(0, frame_idx - 20), frame_idx + 1):
                    if past_frame_idx < len(self.frames):
                        past_states = self.frames[past_frame_idx]['agent_states']
                        for past_state in past_states:
                            if past_state['agent_id'] == agent_id:
                                trajectory_x.append(past_state['position'][0])
                                trajectory_y.append(past_state['position'][1])
                                break
                
                # 绘制轨迹
                if len(trajectory_x) > 1:
                    ax.plot(trajectory_x, trajectory_y, '-', 
                           color=self.agent_colors[agent_idx % len(self.agent_colors)], 
                           alpha=0.6, linewidth=2)
        
        # 绘制当前智能体位置和FOV
        for agent_idx, agent_state in enumerate(agent_states):
            pos = agent_state['position']
            direction = agent_state['direction']
            color = self.agent_colors[agent_idx % len(self.agent_colors)]
            
            # 智能体位置
            ax.plot(pos[0], pos[1], 'o', color=color, markersize=10, markeredgecolor='white', markeredgewidth=2)
            
            # 朝向箭头
            arrow_length = 1.5
            ax.arrow(pos[0], pos[1], 
                    direction[0] * arrow_length, direction[1] * arrow_length,
                    head_width=0.3, head_length=0.3, fc=color, ec=color)
            
            # FOV扇形
            self._draw_fov_sector(ax, pos, direction, color)
            
            # 智能体ID标签
            ax.text(pos[0] + 0.5, pos[1] + 0.5, f'A{agent_state["agent_id"]}', 
                   color='black', fontweight='bold', fontsize=10, 
                   bbox=dict(boxstyle='round,pad=0.2', facecolor='white', alpha=0.8))
        
        ax.set_title(f'Ground Truth Maze (Step {step})')
        ax.set_xlim(-0.5, self.maze_size - 0.5)
        ax.set_ylim(-0.5, self.maze_size - 0.5)
        ax.grid(True, alpha=0.3)
    
    def _draw_fov_sector(self, ax, position, direction, color):
        """绘制FOV扇形"""
        fov_angle = math.radians(75)  # 75度FOV
        max_range = 6.0
        
        facing_angle = math.atan2(direction[1], direction[0])
        start_angle = math.degrees(facing_angle - fov_angle / 2)
        end_angle = math.degrees(facing_angle + fov_angle / 2)
        
        # 创建扇形
        wedge = Wedge(position, max_range, start_angle, end_angle, 
                     facecolor=color, alpha=0.2, edgecolor=color, linewidth=1)
        ax.add_patch(wedge)
    
    def _plot_sectors(self, ax, sectors, agent_states):
        """绘制扇区分割"""
        # 创建扇区可视化
        sector_viz = np.zeros((self.maze_size, self.maze_size, 4))
        
        for i, sector_mask in enumerate(sectors):
            color = self.sector_colors[i % len(self.sector_colors)]
            sector_viz[sector_mask] = color
        
        ax.imshow(sector_viz, origin='lower')
        
        # 标记智能体位置
        for agent_idx, agent_state in enumerate(agent_states):
            pos = agent_state['position']
            color = self.agent_colors[agent_idx % len(self.agent_colors)]
            ax.plot(pos[0], pos[1], 'o', color=color, markersize=8, markeredgecolor='white', markeredgewidth=2)
            ax.text(pos[0] + 0.3, pos[1] + 0.3, f'A{agent_state["agent_id"]}', 
                   color='black', fontweight='bold', fontsize=9,
                   bbox=dict(boxstyle='round,pad=0.1', facecolor='white', alpha=0.9))
        
        ax.set_title('Sector Division')
        ax.set_xlim(-0.5, self.maze_size - 0.5)
        ax.set_ylim(-0.5, self.maze_size - 0.5)
    
    def _plot_agent_observations(self, ax, agent_states):
        """绘制智能体融合观察地图"""
        # 创建融合观察地图（基于SLAM地图）
        fused_map = np.full((self.maze_size, self.maze_size), -1, dtype=np.int8)

        for agent_state in agent_states:
            slam_map = agent_state['slam_map']
            mask = slam_map != -1
            fused_map[mask] = slam_map[mask]

        # 显示融合地图：未知=-1(灰色), 墙壁=0(黑色), 通路=1(白色)
        display_map = np.where(fused_map == -1, 0.5, fused_map)
        ax.imshow(display_map, cmap='gray', origin='lower', vmin=0, vmax=1)

        # 标记智能体当前位置
        for agent_idx, agent_state in enumerate(agent_states):
            pos = agent_state['position']
            color = self.agent_colors[agent_idx % len(self.agent_colors)]
            ax.plot(pos[0], pos[1], 'o', color=color, markersize=6, markeredgecolor='white', markeredgewidth=1)

        ax.set_title('Fused SLAM Map (All Agents)')
        ax.set_xlim(-0.5, self.maze_size - 0.5)
        ax.set_ylim(-0.5, self.maze_size - 0.5)
        ax.grid(True, alpha=0.3)

    def _plot_individual_slam_maps(self, ax, agent_states, step):
        """绘制单个智能体SLAM地图对比"""
        ax.clear()

        # 如果只有一个agent，显示其SLAM地图
        if len(agent_states) == 1:
            agent_state = agent_states[0]
            slam_map = agent_state['slam_map']
            display_map = np.where(slam_map == -1, 0.5, slam_map)
            ax.imshow(display_map, cmap='gray', origin='lower', vmin=0, vmax=1)

            pos = agent_state['position']
            color = self.agent_colors[0]
            ax.plot(pos[0], pos[1], 'o', color=color, markersize=6, markeredgecolor='white', markeredgewidth=1)

            ax.set_title(f'Agent {agent_state["agent_id"]} SLAM Map')
        else:
            # 多个agent时，显示统计信息
            info_text = f"Step: {step}\n\n"

            total_exploration = 0
            total_reward = 0

            for agent_idx, agent_state in enumerate(agent_states):
                exploration = np.sum(agent_state['slam_map'] != -1)
                reward = agent_state.get('reward_score', 0)

                total_exploration += exploration
                total_reward += reward

                color = self.agent_colors[agent_idx % len(self.agent_colors)]
                info_text += f"Agent {agent_state['agent_id']}:\n"
                info_text += f"  SLAM Cells: {exploration}\n"
                info_text += f"  Reward: {reward:.1f}\n"
                info_text += f"  Position: ({agent_state['position'][0]:.1f}, {agent_state['position'][1]:.1f})\n\n"

            info_text += f"Team Total:\n"
            info_text += f"  Total SLAM: {total_exploration} cells\n"
            info_text += f"  Total Reward: {total_reward:.1f}\n"

            # 计算融合地图覆盖率
            fused_map = np.full((self.maze_size, self.maze_size), -1, dtype=np.int8)
            for agent_state in agent_states:
                slam_map = agent_state['slam_map']
                mask = slam_map != -1
                fused_map[mask] = slam_map[mask]

            total_known = np.sum(fused_map != -1)
            total_cells = self.maze_size * self.maze_size
            coverage_rate = total_known / total_cells

            info_text += f"  Coverage: {coverage_rate:.1%}"

            ax.text(0.05, 0.95, info_text, transform=ax.transAxes, fontsize=10,
                   verticalalignment='top', fontfamily='monospace',
                   bbox=dict(boxstyle='round,pad=0.5', facecolor='lightgray', alpha=0.8))

            ax.set_title('Individual SLAM Statistics')

        ax.set_xlim(-0.5, self.maze_size - 0.5)
        ax.set_ylim(-0.5, self.maze_size - 0.5)

    def _plot_statistics(self, ax, agent_states, step):
        """绘制统计信息"""
        ax.axis('off')  # 不显示坐标轴
        
        # 统计信息文本
        info_text = f"Step: {step}\n\n"
        
        total_exploration = 0
        total_reward = 0
        
        for agent_idx, agent_state in enumerate(agent_states):
            exploration = agent_state['exploration_area']
            reward = agent_state['reward_score']
            
            total_exploration += exploration
            total_reward += reward
            
            color = self.agent_colors[agent_idx % len(self.agent_colors)]
            info_text += f"Agent {agent_state['agent_id']}:\n"
            info_text += f"  Position: ({agent_state['position'][0]:.1f}, {agent_state['position'][1]:.1f})\n"
            info_text += f"  Exploration: {exploration:.0f} cells\n"
            info_text += f"  Reward: {reward:.1f}\n"
            info_text += f"  Steps: {agent_state['step_count']}\n\n"
        
        info_text += f"Team Total:\n"
        info_text += f"  Exploration: {total_exploration:.0f} cells\n"
        info_text += f"  Total Reward: {total_reward:.1f}\n"
        
        # 计算探索覆盖率
        fused_map = np.full((self.maze_size, self.maze_size), -1, dtype=np.int8)
        for agent_state in agent_states:
            local_map = agent_state['local_map']
            mask = local_map != -1
            fused_map[mask] = local_map[mask]
        
        total_known = np.sum(fused_map != -1)
        total_cells = self.maze_size * self.maze_size
        coverage_rate = total_known / total_cells
        
        info_text += f"  Coverage: {coverage_rate:.1%}"
        
        ax.text(0.05, 0.95, info_text, transform=ax.transAxes, fontsize=10, 
               verticalalignment='top', fontfamily='monospace',
               bbox=dict(boxstyle='round,pad=0.5', facecolor='lightgray', alpha=0.8))
    
    def create_comparison_video(self, filename: str, other_recorder: 'VideoRecorder', 
                              labels: List[str] = None, fps: int = 5):
        """
        创建对比视频
        Args:
            filename: 输出文件名
            other_recorder: 另一个录制器
            labels: 标签列表
            fps: 帧率
        """
        if not self.frames or not other_recorder.frames:
            print("需要两个录制器都有帧数据")
            return
        
        if labels is None:
            labels = ['Recorder 1', 'Recorder 2']
        
        print(f"创建对比视频，共{min(len(self.frames), len(other_recorder.frames))}帧...")
        
        # 创建对比图形
        fig, axes = plt.subplots(2, 4, figsize=(16, 8))
        fig.suptitle('Multi-Agent SLAM Comparison', fontsize=16)
        
        def animate_comparison(frame_idx):
            for ax in axes.flat:
                ax.clear()
            
            # 绘制第一个录制器的结果
            if frame_idx < len(self.frames):
                frame1 = self.frames[frame_idx]
                self._plot_maze_with_agents(axes[0, 0], frame1['ground_truth_maze'], 
                                          frame1['agent_states'], frame_idx, frame1['step'])
                self._plot_agent_observations(axes[0, 1], frame1['agent_states'])
                axes[0, 0].set_title(f'{labels[0]} - Maze (Step {frame1["step"]})')
                axes[0, 1].set_title(f'{labels[0]} - Observations')
            
            # 绘制第二个录制器的结果
            if frame_idx < len(other_recorder.frames):
                frame2 = other_recorder.frames[frame_idx]
                other_recorder._plot_maze_with_agents(axes[1, 0], frame2['ground_truth_maze'], 
                                                    frame2['agent_states'], frame_idx, frame2['step'])
                other_recorder._plot_agent_observations(axes[1, 1], frame2['agent_states'])
                axes[1, 0].set_title(f'{labels[1]} - Maze (Step {frame2["step"]})')
                axes[1, 1].set_title(f'{labels[1]} - Observations')
            
            plt.tight_layout()
            return axes.flat
        
        # 创建动画
        num_frames = min(len(self.frames), len(other_recorder.frames))
        anim = animation.FuncAnimation(
            fig, animate_comparison, frames=num_frames,
            interval=1000//fps, blit=False, repeat=True
        )
        
        # 保存视频
        try:
            Writer = animation.writers['ffmpeg']
            writer = Writer(fps=fps, metadata=dict(artist='SLAM Validator'), bitrate=1800)
            anim.save(filename, writer=writer, dpi=100)
            print(f"对比视频已保存: {filename}")
        except Exception as e:
            print(f"保存对比视频失败: {e}")
        
        plt.close(fig)


if __name__ == "__main__":
    # 测试视频录制器
    from agent_manager import AgentManager, AgentState
    import numpy as np
    
    # 创建测试数据
    maze_size = 10
    num_agents = 2
    
    # 创建测试迷宫
    test_maze = np.ones((maze_size, maze_size), dtype=np.uint8)
    test_maze[3:6, 3:6] = 0
    test_maze[1:3, 7:9] = 0
    
    # 创建智能体管理器
    agent_manager = AgentManager(maze_size, num_agents)
    agent_manager.initialize_agents(test_maze)
    
    # 创建视频录制器
    recorder = VideoRecorder(maze_size)
    recorder.initialize_recording()
    
    # 模拟智能体运动
    for step in range(30):
        for agent_state in agent_manager.agent_states:
            # 随机移动
            movement = np.random.uniform(-0.3, 0.3, 2)
            new_pos = agent_state.position + movement
            new_pos = np.clip(new_pos, [0.5, 0.5], [maze_size - 0.5, maze_size - 0.5])
            agent_state.position = new_pos
            
            # 随机转向
            if np.random.random() < 0.2:
                angle = np.random.uniform(0, 2 * np.pi)
                agent_state.direction = np.array([np.cos(angle), np.sin(angle)])
            
            # 模拟探索
            for _ in range(3):
                x = np.random.randint(0, maze_size)
                y = np.random.randint(0, maze_size)
                if agent_state.local_map[y, x] == -1:
                    agent_state.local_map[y, x] = test_maze[y, x]
            
            agent_state.exploration_area = np.sum(agent_state.local_map != -1)
            agent_state.reward_score += np.random.uniform(-1, 3)
            agent_state.step_count += 1
        
        # 添加帧
        recorder.add_frame(test_maze, agent_manager.agent_states, 
                          agent_manager.sector_manager.sectors, step)
    
    # 保存视频
    recorder.save_video("test_simulation.gif", fps=3)
    print("测试视频生成完成!")