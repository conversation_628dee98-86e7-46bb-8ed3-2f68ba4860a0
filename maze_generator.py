# -*- coding: utf-8 -*-
# File: maze_generator.py
#!/usr/bin/env python3
"""
房间式迷宫生成器 - 生成Memory Maze风格的多房间迷宫
Room-based Maze Generator - Generates Memory Maze style multi-room mazes
"""

import numpy as np
import random
import json
import os
from typing import List, Tuple, Optional
from dataclasses import dataclass
from PIL import Image


@dataclass
class Room:
    """房间数据结构"""
    x: int
    y: int
    width: int
    height: int
    center: Tuple[int, int]


class RoomBasedMazeGenerator:
    """房间式迷宫生成器"""

    def __init__(self, maze_size: int = 15, seed: Optional[int] = None, dataset_path: Optional[str] = None):
        self.maze_size = maze_size
        self.dataset_path = dataset_path or "/data1/fangzr/Research/Memory_Maze_3D/Dataset/self-navigation-mini"
        if seed is not None:
            np.random.seed(seed)
            random.seed(seed)
    
    def generate_room_based_maze(self, num_rooms: int = 4, complexity: float = 0.7) -> np.ndarray:
        """
        生成基于房间的迷宫
        Args:
            num_rooms: 房间数量 (4-5)
            complexity: 复杂度 [0, 1]
        Returns:
            maze: 二进制数组，1为通路(白色)，0为墙壁(黑色)
        """
        # 初始化全为墙壁
        maze = np.zeros((self.maze_size, self.maze_size), dtype=np.uint8)
        
        # 生成房间
        rooms = self._generate_rooms(num_rooms)
        
        # 在迷宫中挖出房间
        for room in rooms:
            self._carve_room(maze, room)
        
        # 连接房间
        self._connect_rooms(maze, rooms)
        
        # 添加额外的通道和细节
        self._add_corridors(maze, complexity)
        
        # 确保边界是墙壁
        maze[0, :] = 0
        maze[-1, :] = 0
        maze[:, 0] = 0
        maze[:, -1] = 0
        
        return maze

    def load_random_dataset_maze(self) -> Tuple[np.ndarray, dict]:
        """
        从数据集中随机加载一个迷宫布局
        Returns:
            maze: 二进制数组，1为通路(白色)，0为墙壁(黑色)
            config: 环境配置信息
        """
        # 获取所有可用的数据集目录
        available_dirs = []
        if os.path.exists(self.dataset_path):
            for item in os.listdir(self.dataset_path):
                if item.startswith('D1_P') and os.path.isdir(os.path.join(self.dataset_path, item)):
                    available_dirs.append(item)

        if not available_dirs:
            print(f"警告: 在 {self.dataset_path} 中未找到数据集目录，使用生成的迷宫")
            return self.generate_room_based_maze(), {}

        # 随机选择一个目录
        selected_dir = random.choice(available_dirs)
        maze_dir = os.path.join(self.dataset_path, selected_dir, "000000")

        print(f"从数据集加载迷宫: {selected_dir}")

        # 加载环境配置
        config_path = os.path.join(maze_dir, "env_config.json")
        config = {}
        if os.path.exists(config_path):
            try:
                with open(config_path, 'r') as f:
                    config = json.load(f)
            except Exception as e:
                print(f"加载配置文件失败: {e}")

        # 尝试从PNG文件加载迷宫布局
        layout_path = os.path.join(self.dataset_path, selected_dir, "path_visualization", "maze_layout_000000.png")
        if os.path.exists(layout_path):
            try:
                maze = self._load_maze_from_png(layout_path)
                if maze is not None:
                    return maze, config
            except Exception as e:
                print(f"从PNG加载迷宫失败: {e}")

        # 如果PNG加载失败，尝试从配置重建迷宫
        if config and 'maze_config' in config:
            try:
                maze = self._reconstruct_maze_from_config(config)
                return maze, config
            except Exception as e:
                print(f"从配置重建迷宫失败: {e}")

        # 如果都失败了，使用生成的迷宫
        print("数据集迷宫加载失败，使用生成的迷宫")
        return self.generate_room_based_maze(), {}

    def _load_maze_from_png(self, png_path: str) -> Optional[np.ndarray]:
        """从PNG文件加载迷宫布局"""
        try:
            # 加载PNG图像
            img = Image.open(png_path)
            img_array = np.array(img)

            # 如果是彩色图像，转换为灰度
            if len(img_array.shape) == 3:
                img_array = np.mean(img_array, axis=2)

            # 调整大小到目标尺寸
            if img_array.shape != (self.maze_size, self.maze_size):
                img = img.resize((self.maze_size, self.maze_size), Image.NEAREST)
                img_array = np.array(img)
                if len(img_array.shape) == 3:
                    img_array = np.mean(img_array, axis=2)

            # 二值化：假设白色/亮色为通路(1)，黑色/暗色为墙壁(0)
            threshold = np.mean(img_array)
            maze = (img_array > threshold).astype(np.uint8)

            # 确保边界是墙壁
            maze[0, :] = 0
            maze[-1, :] = 0
            maze[:, 0] = 0
            maze[:, -1] = 0

            return maze

        except Exception as e:
            print(f"PNG加载错误: {e}")
            return None

    def _reconstruct_maze_from_config(self, config: dict) -> np.ndarray:
        """从配置信息重建迷宫（简化版本）"""
        # 这是一个简化的重建方法，基于配置中的房间信息
        maze_config = config.get('maze_config', {})
        maze_size = maze_config.get('maze_size', self.maze_size)
        max_rooms = maze_config.get('max_rooms', 4)

        # 使用现有的房间生成方法
        return self.generate_room_based_maze(num_rooms=max_rooms)

    def _generate_rooms(self, num_rooms: int) -> List[Room]:
        """生成房间布局"""
        rooms = []
        min_room_size = 3
        max_room_size = 6
        
        attempts = 0
        max_attempts = 100
        
        while len(rooms) < num_rooms and attempts < max_attempts:
            # 随机房间尺寸
            width = random.randint(min_room_size, max_room_size)
            height = random.randint(min_room_size, max_room_size)
            
            # 随机房间位置
            x = random.randint(1, self.maze_size - width - 1)
            y = random.randint(1, self.maze_size - height - 1)
            
            new_room = Room(
                x=x, y=y, width=width, height=height,
                center=(x + width // 2, y + height // 2)
            )
            
            # 检查是否与现有房间重叠
            if not self._rooms_overlap(new_room, rooms):
                rooms.append(new_room)
            
            attempts += 1
        
        return rooms
    
    def _rooms_overlap(self, new_room: Room, existing_rooms: List[Room]) -> bool:
        """检查房间是否重叠"""
        for room in existing_rooms:
            # 添加1格间距避免房间紧邻
            if (new_room.x < room.x + room.width + 1 and
                new_room.x + new_room.width + 1 > room.x and
                new_room.y < room.y + room.height + 1 and
                new_room.y + new_room.height + 1 > room.y):
                return True
        return False
    
    def _carve_room(self, maze: np.ndarray, room: Room) -> None:
        """在迷宫中挖出房间"""
        for y in range(room.y, room.y + room.height):
            for x in range(room.x, room.x + room.width):
                if 0 <= x < self.maze_size and 0 <= y < self.maze_size:
                    maze[y, x] = 1
    
    def _connect_rooms(self, maze: np.ndarray, rooms: List[Room]) -> None:
        """连接所有房间"""
        if len(rooms) < 2:
            return
        
        # 使用最小生成树算法连接房间
        connected = [rooms[0]]
        unconnected = rooms[1:]
        
        while unconnected:
            min_distance = float('inf')
            closest_pair = None
            
            # 找到最近的房间对
            for connected_room in connected:
                for unconnected_room in unconnected:
                    distance = self._room_distance(connected_room, unconnected_room)
                    if distance < min_distance:
                        min_distance = distance
                        closest_pair = (connected_room, unconnected_room)
            
            if closest_pair:
                # 连接最近的房间对
                self._create_corridor(maze, closest_pair[0], closest_pair[1])
                connected.append(closest_pair[1])
                unconnected.remove(closest_pair[1])
        
        # 添加一些额外的连接增加连通性
        self._add_extra_connections(maze, rooms)
    
    def _room_distance(self, room1: Room, room2: Room) -> float:
        """计算两个房间中心的距离"""
        dx = room1.center[0] - room2.center[0]
        dy = room1.center[1] - room2.center[1]
        return np.sqrt(dx * dx + dy * dy)
    
    def _create_corridor(self, maze: np.ndarray, room1: Room, room2: Room) -> None:
        """在两个房间之间创建通道"""
        x1, y1 = room1.center
        x2, y2 = room2.center
        
        # L形通道：先水平移动，再垂直移动
        if random.random() < 0.5:
            # 先水平后垂直
            self._carve_horizontal_corridor(maze, x1, x2, y1)
            self._carve_vertical_corridor(maze, y1, y2, x2)
        else:
            # 先垂直后水平
            self._carve_vertical_corridor(maze, y1, y2, x1)
            self._carve_horizontal_corridor(maze, x1, x2, y2)
    
    def _carve_horizontal_corridor(self, maze: np.ndarray, x1: int, x2: int, y: int) -> None:
        """挖掘水平通道"""
        start_x = min(x1, x2)
        end_x = max(x1, x2)
        for x in range(start_x, end_x + 1):
            if 0 <= x < self.maze_size and 0 <= y < self.maze_size:
                maze[y, x] = 1
    
    def _carve_vertical_corridor(self, maze: np.ndarray, y1: int, y2: int, x: int) -> None:
        """挖掘垂直通道"""
        start_y = min(y1, y2)
        end_y = max(y1, y2)
        for y in range(start_y, end_y + 1):
            if 0 <= x < self.maze_size and 0 <= y < self.maze_size:
                maze[y, x] = 1
    
    def _add_extra_connections(self, maze: np.ndarray, rooms: List[Room]) -> None:
        """添加额外的房间连接"""
        num_extra = random.randint(1, max(1, len(rooms) // 2))
        
        for _ in range(num_extra):
            room1 = random.choice(rooms)
            room2 = random.choice(rooms)
            if room1 != room2:
                # 30% 概率添加额外连接
                if random.random() < 0.3:
                    self._create_corridor(maze, room1, room2)
    
    def _add_corridors(self, maze: np.ndarray, complexity: float) -> None:
        """根据复杂度添加额外的通道"""
        num_corridors = int(complexity * self.maze_size * 0.5)
        
        for _ in range(num_corridors):
            # 随机选择起点
            start_x = random.randint(1, self.maze_size - 2)
            start_y = random.randint(1, self.maze_size - 2)
            
            if maze[start_y, start_x] == 1:  # 必须从通路开始
                # 随机方向挖掘短通道
                direction = random.choice([(0, 1), (1, 0), (0, -1), (-1, 0)])
                length = random.randint(2, 4)
                
                x, y = start_x, start_y
                for _ in range(length):
                    x += direction[0]
                    y += direction[1]
                    
                    if (0 < x < self.maze_size - 1 and 
                        0 < y < self.maze_size - 1):
                        maze[y, x] = 1
                    else:
                        break
    
    def generate_correlated_maze(self, base_maze: np.ndarray, correlation: float = 0.8) -> np.ndarray:
        """
        基于基础迷宫生成相关迷宫
        Args:
            base_maze: 基础迷宫
            correlation: 相关性 [0, 1]
        """
        correlated_maze = base_maze.copy()
        
        # 计算需要改变的像素数量
        total_pixels = self.maze_size * self.maze_size
        change_pixels = int(total_pixels * (1 - correlation))
        
        # 只在非边界区域进行修改
        for _ in range(change_pixels):
            x = random.randint(1, self.maze_size - 2)
            y = random.randint(1, self.maze_size - 2)
            
            # 避免完全封闭通路
            if correlated_maze[y, x] == 1:  # 如果是通路
                # 检查周围是否有足够的通路
                neighbors = [
                    correlated_maze[y-1, x], correlated_maze[y+1, x],
                    correlated_maze[y, x-1], correlated_maze[y, x+1]
                ]
                if sum(neighbors) > 2:  # 有多个通路邻居才能变成墙
                    correlated_maze[y, x] = 0
            else:  # 如果是墙壁
                correlated_maze[y, x] = 1
        
        return correlated_maze
    
    def visualize_maze(self, maze: np.ndarray, title: str = "Generated Maze") -> None:
        """可视化迷宫"""
        import matplotlib.pyplot as plt
        
        plt.figure(figsize=(8, 8))
        plt.imshow(maze, cmap='gray', origin='lower')
        plt.title(title)
        plt.grid(True, alpha=0.3)
        plt.colorbar(label='0=Wall, 1=Path')
        plt.show()


if __name__ == "__main__":
    # 测试迷宫生成器
    generator = RoomBasedMazeGenerator(maze_size=15, seed=42)
    
    # 生成多个测试迷宫
    for i in range(3):
        maze = generator.generate_room_based_maze(num_rooms=random.randint(4, 5))
        generator.visualize_maze(maze, f"Test Maze {i+1}")
        
        # 检查连通性
        path_cells = np.sum(maze == 1)
        total_cells = maze.size
        print(f"迷宫 {i+1}: 通路占比 {path_cells/total_cells:.2%}")
