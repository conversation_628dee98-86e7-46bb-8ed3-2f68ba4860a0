#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
社交位置细胞(SPC)分析器
Social Place Cell (SPC) Analyzer

分析训练后的SentryNet中候选SPC的激活模式，
验证网络是否学会了对边界区域的选择性响应。
"""

import torch
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, List, Tuple, Optional
import os
from dataclasses import dataclass
from sklearn.cluster import KMeans
from sklearn.decomposition import PCA
from scipy.stats import pearsonr

from sentry_net import SentryNet, SentryNetConfig
from sentry_data_collector import SentryDataCollector, DataCollectionConfig, PeerObservationExtractor
from agent_manager import AgentManager, AgentState
from maze_generator import RoomBasedMazeGenerator


@dataclass
class SPCAnalysisConfig:
    """SPC分析配置"""
    model_path: str = "sentry_models/best_model.pth"
    analysis_episodes: int = 20
    steps_per_episode: int = 100
    maze_size: int = 15
    num_agents: int = 3
    
    # 边界定义
    boundary_threshold: float = 2.0  # 距离边界多近算作边界区域
    
    # 分析参数
    num_spc_clusters: int = 8  # SPC聚类数量
    activation_threshold: float = 0.5  # 激活阈值
    
    # 可视化
    save_plots: bool = True
    plot_dir: str = "spc_analysis_plots"


class BoundaryDetector:
    """边界检测器"""
    
    def __init__(self, maze_size: int, boundary_threshold: float):
        self.maze_size = maze_size
        self.boundary_threshold = boundary_threshold
    
    def is_near_boundary(self, position: np.ndarray, sector_id: int) -> bool:
        """
        判断位置是否靠近边界
        Args:
            position: [x, y] 位置坐标
            sector_id: 扇区ID
        Returns:
            bool: 是否靠近边界
        """
        x, y = position
        
        # 检查是否靠近迷宫边界
        if (x < self.boundary_threshold or 
            x > self.maze_size - self.boundary_threshold or
            y < self.boundary_threshold or 
            y > self.maze_size - self.boundary_threshold):
            return True
        
        # 检查是否靠近扇区边界（简化版本）
        # 这里可以根据具体的扇区划分规则来实现
        center_x, center_y = self.maze_size // 2, self.maze_size // 2
        
        # 根据扇区ID判断边界
        if sector_id == 0:  # 左上扇区
            if x > center_x - self.boundary_threshold or y > center_y - self.boundary_threshold:
                return True
        elif sector_id == 1:  # 右上扇区
            if x < center_x + self.boundary_threshold or y > center_y - self.boundary_threshold:
                return True
        elif sector_id == 2:  # 左下扇区
            if x > center_x - self.boundary_threshold or y < center_y + self.boundary_threshold:
                return True
        
        return False
    
    def get_boundary_distance(self, position: np.ndarray, sector_id: int) -> float:
        """
        计算到最近边界的距离
        Args:
            position: [x, y] 位置坐标
            sector_id: 扇区ID
        Returns:
            distance: 到边界的最小距离
        """
        x, y = position
        
        # 到迷宫边界的距离
        maze_boundary_dist = min(x, y, self.maze_size - x, self.maze_size - y)
        
        # 到扇区边界的距离（简化版本）
        center_x, center_y = self.maze_size // 2, self.maze_size // 2
        
        if sector_id == 0:  # 左上扇区
            sector_boundary_dist = min(center_x - x, center_y - y)
        elif sector_id == 1:  # 右上扇区
            sector_boundary_dist = min(x - center_x, center_y - y)
        elif sector_id == 2:  # 左下扇区
            sector_boundary_dist = min(center_x - x, y - center_y)
        else:
            sector_boundary_dist = float('inf')
        
        return min(maze_boundary_dist, sector_boundary_dist)


class SPCAnalyzer:
    """SPC分析器主类"""
    
    def __init__(self, config: SPCAnalysisConfig):
        self.config = config
        self.boundary_detector = BoundaryDetector(config.maze_size, config.boundary_threshold)
        
        # 加载训练好的模型
        self.model = self._load_model()
        self.model.eval()
        
        # 数据收集器
        collection_config = DataCollectionConfig(
            maze_size=config.maze_size,
            num_agents=config.num_agents
        )
        self.extractor = PeerObservationExtractor(collection_config)
        self.maze_generator = RoomBasedMazeGenerator(config.maze_size)
        
        # 分析结果存储
        self.activation_data = []
        self.position_data = []
        self.boundary_labels = []
        
        # 创建输出目录
        if config.save_plots:
            os.makedirs(config.plot_dir, exist_ok=True)
    
    def _load_model(self) -> SentryNet:
        """加载训练好的模型"""
        if not os.path.exists(self.config.model_path):
            raise FileNotFoundError(f"模型文件不存在: {self.config.model_path}")
        
        checkpoint = torch.load(self.config.model_path, map_location='cpu')
        sentry_config = checkpoint['sentry_config']
        
        model = SentryNet(sentry_config)
        model.load_state_dict(checkpoint['model_state_dict'])
        
        print(f"模型已从 {self.config.model_path} 加载")
        return model
    
    def collect_activation_data(self) -> None:
        """收集SPC激活数据"""
        print("收集SPC激活数据...")
        
        for episode in range(self.config.analysis_episodes):
            print(f"Episode {episode + 1}/{self.config.analysis_episodes}")
            
            # 生成新迷宫
            maze = self.maze_generator.generate_room_based_maze()
            
            # 初始化智能体
            agent_manager = AgentManager(self.config.maze_size, self.config.num_agents)
            agent_manager.initialize_agents(maze)
            
            # 仿真步骤
            for step in range(self.config.steps_per_episode):
                for agent_id, agent_state in enumerate(agent_manager.agent_states):
                    # 提取观察数据
                    peer_patch = self.extractor.extract_peer_local_patch(agent_state, maze)
                    peer_velocity = np.random.randn(2) * 0.1  # 模拟速度
                    
                    # 转换为tensor
                    patch_tensor = torch.FloatTensor(peer_patch).unsqueeze(0)
                    velocity_tensor = torch.FloatTensor(peer_velocity).unsqueeze(0)
                    
                    # 获取SPC激活
                    with torch.no_grad():
                        hidden_state = self.model.reset_hidden_state(1)
                        outputs = self.model(patch_tensor, velocity_tensor, hidden_state)
                        attention_key = outputs['attention_key'].squeeze(0).numpy()
                    
                    # 判断是否靠近边界
                    is_boundary = self.boundary_detector.is_near_boundary(
                        agent_state.position, agent_state.sector
                    )
                    boundary_distance = self.boundary_detector.get_boundary_distance(
                        agent_state.position, agent_state.sector
                    )
                    
                    # 存储数据
                    self.activation_data.append(attention_key)
                    self.position_data.append(agent_state.position.copy())
                    self.boundary_labels.append({
                        'is_boundary': is_boundary,
                        'distance': boundary_distance,
                        'sector': agent_state.sector,
                        'agent_id': agent_id
                    })
                    
                    # 随机移动智能体
                    movement = np.random.uniform(-0.5, 0.5, 2)
                    new_pos = agent_state.position + movement
                    new_pos = np.clip(new_pos, [1, 1], [self.config.maze_size-1, self.config.maze_size-1])
                    agent_state.position = new_pos
        
        print(f"收集完成！总共 {len(self.activation_data)} 个样本")
    
    def analyze_spc_selectivity(self) -> Dict:
        """分析SPC选择性"""
        print("分析SPC选择性...")
        
        activations = np.array(self.activation_data)
        positions = np.array(self.position_data)
        
        # 分离边界和非边界样本
        boundary_mask = np.array([label['is_boundary'] for label in self.boundary_labels])
        boundary_activations = activations[boundary_mask]
        non_boundary_activations = activations[~boundary_mask]
        
        print(f"边界样本: {len(boundary_activations)}")
        print(f"非边界样本: {len(non_boundary_activations)}")
        
        # 计算每个神经元的选择性指数
        selectivity_indices = []
        for i in range(activations.shape[1]):
            boundary_mean = np.mean(boundary_activations[:, i])
            non_boundary_mean = np.mean(non_boundary_activations[:, i])
            
            # 选择性指数：(边界激活 - 非边界激活) / (边界激活 + 非边界激活)
            if boundary_mean + non_boundary_mean > 0:
                selectivity = (boundary_mean - non_boundary_mean) / (boundary_mean + non_boundary_mean)
            else:
                selectivity = 0
            
            selectivity_indices.append(selectivity)
        
        selectivity_indices = np.array(selectivity_indices)
        
        # 找出高选择性的神经元（候选SPC）
        high_selectivity_mask = selectivity_indices > self.config.activation_threshold
        candidate_spcs = np.where(high_selectivity_mask)[0]
        
        print(f"发现 {len(candidate_spcs)} 个候选SPC")
        print(f"选择性指数范围: [{selectivity_indices.min():.3f}, {selectivity_indices.max():.3f}]")
        
        return {
            'selectivity_indices': selectivity_indices,
            'candidate_spcs': candidate_spcs,
            'boundary_activations': boundary_activations,
            'non_boundary_activations': non_boundary_activations
        }
    
    def cluster_spc_responses(self, analysis_results: Dict) -> Dict:
        """聚类SPC响应模式"""
        print("聚类SPC响应模式...")
        
        activations = np.array(self.activation_data)
        candidate_spcs = analysis_results['candidate_spcs']
        
        if len(candidate_spcs) == 0:
            print("没有发现候选SPC，跳过聚类分析")
            return {}
        
        # 提取候选SPC的激活
        spc_activations = activations[:, candidate_spcs]
        
        # K-means聚类
        kmeans = KMeans(n_clusters=min(self.config.num_spc_clusters, len(candidate_spcs)), 
                       random_state=42)
        cluster_labels = kmeans.fit_predict(spc_activations)
        
        # PCA降维用于可视化
        pca = PCA(n_components=2)
        spc_pca = pca.fit_transform(spc_activations)
        
        return {
            'cluster_labels': cluster_labels,
            'cluster_centers': kmeans.cluster_centers_,
            'spc_pca': spc_pca,
            'pca_explained_variance': pca.explained_variance_ratio_
        }
    
    def visualize_results(self, analysis_results: Dict, cluster_results: Dict) -> None:
        """可视化分析结果"""
        print("生成可视化结果...")
        
        # 1. 选择性指数分布
        plt.figure(figsize=(12, 8))
        
        plt.subplot(2, 3, 1)
        selectivity_indices = analysis_results['selectivity_indices']
        plt.hist(selectivity_indices, bins=30, alpha=0.7)
        plt.axvline(self.config.activation_threshold, color='red', linestyle='--', 
                   label=f'Threshold ({self.config.activation_threshold})')
        plt.xlabel('Selectivity Index')
        plt.ylabel('Count')
        plt.title('SPC Selectivity Distribution')
        plt.legend()
        
        # 2. 候选SPC激活对比
        plt.subplot(2, 3, 2)
        candidate_spcs = analysis_results['candidate_spcs']
        if len(candidate_spcs) > 0:
            boundary_activations = analysis_results['boundary_activations']
            non_boundary_activations = analysis_results['non_boundary_activations']
            
            boundary_mean = np.mean(boundary_activations[:, candidate_spcs], axis=0)
            non_boundary_mean = np.mean(non_boundary_activations[:, candidate_spcs], axis=0)
            
            x = np.arange(len(candidate_spcs))
            width = 0.35
            
            plt.bar(x - width/2, boundary_mean, width, label='Boundary', alpha=0.7)
            plt.bar(x + width/2, non_boundary_mean, width, label='Non-boundary', alpha=0.7)
            plt.xlabel('Candidate SPC Index')
            plt.ylabel('Mean Activation')
            plt.title('SPC Activation Comparison')
            plt.legend()
        
        # 3. 位置-激活热图
        plt.subplot(2, 3, 3)
        positions = np.array(self.position_data)
        if len(candidate_spcs) > 0:
            # 选择第一个候选SPC
            activations = np.array(self.activation_data)
            first_spc_activations = activations[:, candidate_spcs[0]]
            
            # 创建位置网格
            x_bins = np.linspace(0, self.config.maze_size, 20)
            y_bins = np.linspace(0, self.config.maze_size, 20)
            
            # 计算每个网格的平均激活
            activation_grid = np.zeros((len(y_bins)-1, len(x_bins)-1))
            count_grid = np.zeros((len(y_bins)-1, len(x_bins)-1))
            
            for i, pos in enumerate(positions):
                x_idx = np.digitize(pos[0], x_bins) - 1
                y_idx = np.digitize(pos[1], y_bins) - 1
                
                if 0 <= x_idx < len(x_bins)-1 and 0 <= y_idx < len(y_bins)-1:
                    activation_grid[y_idx, x_idx] += first_spc_activations[i]
                    count_grid[y_idx, x_idx] += 1
            
            # 避免除零
            activation_grid = np.divide(activation_grid, count_grid, 
                                      out=np.zeros_like(activation_grid), 
                                      where=count_grid!=0)
            
            plt.imshow(activation_grid, origin='lower', cmap='hot', interpolation='bilinear')
            plt.colorbar(label='Activation')
            plt.title(f'SPC {candidate_spcs[0]} Spatial Activation')
            plt.xlabel('X Position')
            plt.ylabel('Y Position')
        
        # 4. 聚类结果
        if cluster_results:
            plt.subplot(2, 3, 4)
            spc_pca = cluster_results['spc_pca']
            cluster_labels = cluster_results['cluster_labels']
            
            scatter = plt.scatter(spc_pca[:, 0], spc_pca[:, 1], c=cluster_labels, 
                                cmap='tab10', alpha=0.6)
            plt.xlabel(f'PC1 ({cluster_results["pca_explained_variance"][0]:.2%})')
            plt.ylabel(f'PC2 ({cluster_results["pca_explained_variance"][1]:.2%})')
            plt.title('SPC Response Clusters')
            plt.colorbar(scatter)
        
        # 5. 边界距离vs激活关系
        plt.subplot(2, 3, 5)
        distances = [label['distance'] for label in self.boundary_labels]
        if len(candidate_spcs) > 0:
            activations = np.array(self.activation_data)
            mean_spc_activation = np.mean(activations[:, candidate_spcs], axis=1)
            
            plt.scatter(distances, mean_spc_activation, alpha=0.5)
            
            # 计算相关性
            correlation, p_value = pearsonr(distances, mean_spc_activation)
            plt.xlabel('Distance to Boundary')
            plt.ylabel('Mean SPC Activation')
            plt.title(f'Distance vs Activation (r={correlation:.3f}, p={p_value:.3f})')
        
        # 6. 扇区分布
        plt.subplot(2, 3, 6)
        sectors = [label['sector'] for label in self.boundary_labels]
        boundary_flags = [label['is_boundary'] for label in self.boundary_labels]
        
        sector_boundary_counts = {}
        sector_total_counts = {}
        
        for sector, is_boundary in zip(sectors, boundary_flags):
            if sector not in sector_total_counts:
                sector_total_counts[sector] = 0
                sector_boundary_counts[sector] = 0
            
            sector_total_counts[sector] += 1
            if is_boundary:
                sector_boundary_counts[sector] += 1
        
        sectors_list = list(sector_total_counts.keys())
        boundary_ratios = [sector_boundary_counts[s] / sector_total_counts[s] 
                          for s in sectors_list]
        
        plt.bar(sectors_list, boundary_ratios)
        plt.xlabel('Sector ID')
        plt.ylabel('Boundary Sample Ratio')
        plt.title('Boundary Distribution by Sector')
        
        plt.tight_layout()
        
        if self.config.save_plots:
            plt.savefig(os.path.join(self.config.plot_dir, 'spc_analysis.png'), 
                       dpi=300, bbox_inches='tight')
        
        plt.show()
    
    def run_analysis(self) -> Dict:
        """运行完整的SPC分析"""
        print("开始SPC分析...")
        
        # 收集数据
        self.collect_activation_data()
        
        # 分析选择性
        analysis_results = self.analyze_spc_selectivity()
        
        # 聚类分析
        cluster_results = self.cluster_spc_responses(analysis_results)
        
        # 可视化
        self.visualize_results(analysis_results, cluster_results)
        
        # 生成报告
        report = {
            'total_samples': len(self.activation_data),
            'boundary_samples': sum(label['is_boundary'] for label in self.boundary_labels),
            'candidate_spcs': len(analysis_results['candidate_spcs']),
            'max_selectivity': analysis_results['selectivity_indices'].max(),
            'mean_selectivity': analysis_results['selectivity_indices'].mean()
        }
        
        print("\n=== SPC分析报告 ===")
        for key, value in report.items():
            print(f"{key}: {value}")
        
        return {
            'analysis_results': analysis_results,
            'cluster_results': cluster_results,
            'report': report
        }


if __name__ == "__main__":
    # 运行SPC分析
    config = SPCAnalysisConfig()
    analyzer = SPCAnalyzer(config)
    
    results = analyzer.run_analysis()
