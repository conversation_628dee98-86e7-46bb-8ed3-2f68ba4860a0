{"config": {"test_episodes": 2, "steps_per_episode": 50, "warning_threshold": 0.7, "social_penalty": -0.5}, "episode_stats": [{"total_warnings": 0, "total_penalties": 0, "boundary_violations": 0, "agent_stats": {"0": {"warnings_sent": 0, "warnings_received": 0, "boundary_violations": 0, "social_penalty": 0.0}, "1": {"warnings_sent": 0, "warnings_received": 0, "boundary_violations": 0, "social_penalty": 0.0}, "2": {"warnings_sent": 0, "warnings_received": 0, "boundary_violations": 0, "social_penalty": 0.0}}}, {"total_warnings": 0, "total_penalties": 0, "boundary_violations": 0, "agent_stats": {"0": {"warnings_sent": 0, "warnings_received": 0, "boundary_violations": 0, "social_penalty": 0.0}, "1": {"warnings_sent": 0, "warnings_received": 0, "boundary_violations": 0, "social_penalty": 0.0}, "2": {"warnings_sent": 0, "warnings_received": 0, "boundary_violations": 0, "social_penalty": 0.0}}}], "summary_stats": {"total_episodes": 2, "total_warnings": 0, "total_penalties": 0, "total_violations": 0, "avg_warnings_per_episode": 0.0, "avg_penalties_per_episode": 0.0, "avg_violations_per_episode": 0.0, "warning_effectiveness": 0.0, "agent_summary": {"0": {"total_warnings_sent": 0, "total_warnings_received": 0, "total_violations": 0, "total_penalty": 0.0}, "1": {"total_warnings_sent": 0, "total_warnings_received": 0, "total_violations": 0, "total_penalty": 0.0}, "2": {"total_warnings_sent": 0, "total_warnings_received": 0, "total_violations": 0, "total_penalty": 0.0}}}}