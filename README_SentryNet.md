# SentryNet - 社交哨兵网络

## 项目概述

SentryNet是一个专门用于社会监控的神经网络，能够通过观察同伴的原始感官信息流，学会判断同伴是否处于关键区域（如领地边界），并生成功能性的社交位置细胞(SPC)表征。

### 核心思想

在多智能体环境中，当某个智能体越过边界时，其他智能体需要发送警告。SentryNet通过以下机制实现社会监控：

1. **需求产生（社会监控）**：为了最大化自身奖励，智能体必须避免受到社会惩罚，因此产生了强烈的动机去感知、预测同伴的行为。

2. **表征涌现（社交位置细胞）**：为了准确预测同伴行为，网络中的某些神经元（候选SPC）学会对"同伴在特定关键区域"这一复合事件做出选择性响应。

3. **功能实现（注意力解码）**：候选SPC的激活作为注意力信号，引导解码器更加关注与越界行为相关的信息。

## 项目结构

```
├── sentry_net.py              # SentryNet核心网络架构
├── sentry_data_collector.py   # 数据收集器
├── sentry_trainer.py          # 训练脚本
├── spc_analyzer.py           # SPC分析工具
├── sentry_integration_test.py # 集成测试
├── run_sentry_demo.py        # 完整演示脚本
└── README_SentryNet.md       # 项目说明文档
```

## 网络架构

### SentryNet组件

1. **ObservationEncoder (CNN)**
   - 处理同伴的7x7局部地图切片
   - 2层卷积网络提取空间特征

2. **InputFusion (MLP)**
   - 融合空间特征和同伴速度信息
   - 生成综合的特征表示

3. **MemoryModule (GRU)**
   - 整合时间序列信息
   - 维持对同伴状态的连续追踪

4. **功能头**
   - **PredictiveDecoder**: 预测同伴下一时刻的局部地图
   - **AttentionKeyHead**: 生成候选SPC激活模式
   - **MessageValueHead**: 生成注意力机制中的Value向量

### 训练策略

使用**自监督学习**，通过预测同伴未来的局部地图来训练网络。这个单一的预测任务驱动整个网络学习，包括SPC的涌现。

## 快速开始

### 环境要求

```bash
pip install torch numpy matplotlib scikit-learn scipy seaborn
```

### 完整演示

运行完整的SentryNet演示：

```bash
python run_sentry_demo.py
```

这将依次执行：
1. 数据收集
2. 模型训练
3. SPC分析
4. 集成测试

### 快速演示

如果想要快速体验（使用较少的数据和训练轮数）：

```bash
python run_sentry_demo.py --quick
```

### 分步运行

也可以分步运行各个阶段：

```bash
# 只收集数据
python run_sentry_demo.py --step collect

# 只训练模型
python run_sentry_demo.py --step train

# 只进行SPC分析
python run_sentry_demo.py --step analyze

# 只进行集成测试
python run_sentry_demo.py --step test
```

## 详细使用说明

### 1. 数据收集

```python
from sentry_data_collector import SentryDataCollector, DataCollectionConfig

config = DataCollectionConfig(
    num_episodes=50,
    steps_per_episode=150
)

collector = SentryDataCollector(config)
collector.collect_all_data()
collector.save_data()
```

### 2. 模型训练

```python
from sentry_trainer import SentryTrainer, TrainingConfig
from sentry_net import SentryNetConfig

sentry_config = SentryNetConfig()
training_config = TrainingConfig(num_epochs=50)

trainer = SentryTrainer(sentry_config, training_config)
trainer.train()
```

### 3. SPC分析

```python
from spc_analyzer import SPCAnalyzer, SPCAnalysisConfig

config = SPCAnalysisConfig()
analyzer = SPCAnalyzer(config)
results = analyzer.run_analysis()
```

### 4. 集成测试

```python
from sentry_integration_test import SentryIntegrationTester, IntegrationTestConfig

config = IntegrationTestConfig()
tester = SentryIntegrationTester(config)
results = tester.run_all_tests()
```

## 输出文件

运行完整演示后，将生成以下文件：

- `sentry_training_data/` - 训练数据
- `sentry_models/` - 训练好的模型和训练曲线
- `spc_analysis_plots/` - SPC分析图表
- `sentry_test_results/` - 集成测试结果和视频

## 核心配置参数

### SentryNetConfig
- `patch_size`: 局部地图切片大小 (默认: 7)
- `memory_hidden_dim`: GRU隐藏状态维度 (默认: 128)
- `attention_key_dim`: 注意力Key向量维度 (默认: 64)

### TrainingConfig
- `num_epochs`: 训练轮数 (默认: 100)
- `learning_rate`: 学习率 (默认: 1e-3)
- `batch_size`: 批大小 (默认: 32)

### SPCAnalysisConfig
- `boundary_threshold`: 边界阈值 (默认: 2.0)
- `activation_threshold`: SPC激活阈值 (默认: 0.5)
- `num_spc_clusters`: SPC聚类数量 (默认: 8)

## 实验结果解读

### SPC分析结果

1. **选择性指数分布**: 显示网络中神经元对边界区域的选择性
2. **候选SPC激活对比**: 比较边界和非边界区域的激活差异
3. **空间激活热图**: 显示SPC在不同位置的激活强度
4. **聚类分析**: 识别不同类型的SPC响应模式

### 集成测试结果

1. **警告统计**: 智能体发送和接收警告的数量
2. **边界违规**: 实际的边界违规次数
3. **社会惩罚**: 应用的社会惩罚总量
4. **警告有效性**: 警告导致惩罚的比例

## 技术特点

1. **分阶段实现**: 先实现SentryNet核心功能，再集成到完整系统
2. **自监督学习**: 无需标注数据，通过预测任务自动学习
3. **功能性涌现**: SPC自然涌现，无需显式设计
4. **注意力机制**: 基于SPC激活的注意力解码
5. **可解释性**: 通过SPC分析理解网络内部机制

## 扩展方向

1. **更复杂的边界定义**: 支持不规则边界和动态边界
2. **多模态输入**: 整合视觉、听觉等多种感官信息
3. **层次化SPC**: 支持多层次的社会监控
4. **在线学习**: 支持在运行时持续学习和适应
5. **群体行为**: 扩展到更大规模的多智能体系统

## 引用

如果您在研究中使用了SentryNet，请引用：

```
SentryNet: A Neural Network for Social Monitoring in Multi-Agent Systems
通过自监督学习实现的社交位置细胞涌现机制
```

## 许可证

本项目采用MIT许可证。详见LICENSE文件。
