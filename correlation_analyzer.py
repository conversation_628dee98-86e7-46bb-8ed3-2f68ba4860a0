# -*- coding: utf-8 -*-
# File: correlation_analyzer.py
#!/usr/bin/env python3
"""
相关性分析器 - 分析不同智能体观察到的地图相关性
Correlation Analyzer - Analyzes correlation between different agents' observed maps
"""

import numpy as np
from typing import Dict, List
from agent_manager import AgentState


class CorrelationAnalyzer:
    """相关性分析器"""
    
    def __init__(self):
        self.correlation_history = []
    
    def calculate_map_correlation(self, map1: np.ndarray, map2: np.ndarray) -> float:
        """
        计算两个地图的相关性（只计算两者都已知的区域）
        Args:
            map1, map2: 两个地图数组 (-1: 未知, 0: 墙壁, 1: 通路)
        Returns:
            correlation: 相关性系数 [-1, 1]
        """
        # 只考虑两个地图都已知的区域
        mask1 = map1 != -1
        mask2 = map2 != -1
        common_mask = mask1 & mask2
        
        if np.sum(common_mask) == 0:
            return 0.0  # 没有共同已知区域
        
        values1 = map1[common_mask].astype(np.float32)
        values2 = map2[common_mask].astype(np.float32)
        
        if len(values1) < 2:
            return 0.0  # 需要至少2个数据点计算相关性
        
        # 检查是否所有值都相同（方差为0）
        var1 = np.var(values1)
        var2 = np.var(values2)
        
        if var1 == 0 or var2 == 0:
            # 如果两个数组都是常数且相等，相关性为1
            if var1 == 0 and var2 == 0:
                return 1.0 if np.mean(values1) == np.mean(values2) else 0.0
            else:
                return 0.0
        
        # 计算皮尔逊相关系数
        try:
            correlation = np.corrcoef(values1, values2)[0, 1]
            return correlation if not np.isnan(correlation) else 0.0
        except:
            return 0.0
    
    def calculate_jaccard_similarity(self, map1: np.ndarray, map2: np.ndarray) -> float:
        """
        计算两个地图的Jaccard相似性（基于已知区域的重叠）
        Args:
            map1, map2: 两个地图数组
        Returns:
            jaccard: Jaccard相似性 [0, 1]
        """
        known1 = map1 != -1
        known2 = map2 != -1
        
        intersection = np.sum(known1 & known2)
        union = np.sum(known1 | known2)
        
        return intersection / union if union > 0 else 0.0
    
    def calculate_overlap_agreement(self, map1: np.ndarray, map2: np.ndarray) -> Dict[str, float]:
        """
        计算重叠区域的一致性
        Args:
            map1, map2: 两个地图数组
        Returns:
            Dict: 包含一致性统计的字典
        """
        # 找到重叠的已知区域
        mask1 = map1 != -1
        mask2 = map2 != -1
        overlap_mask = mask1 & mask2
        
        if np.sum(overlap_mask) == 0:
            return {
                'overlap_size': 0,
                'agreement_rate': 0.0,
                'wall_agreement': 0.0,
                'path_agreement': 0.0
            }
        
        # 提取重叠区域的值
        values1 = map1[overlap_mask]
        values2 = map2[overlap_mask]
        
        # 计算一致性
        total_overlap = len(values1)
        agreements = np.sum(values1 == values2)
        agreement_rate = agreements / total_overlap
        
        # 分别计算墙壁和通路的一致性
        wall_mask = (values1 == 0) | (values2 == 0)
        path_mask = (values1 == 1) | (values2 == 1)
        
        wall_agreement = 0.0
        if np.sum(wall_mask) > 0:
            wall_agreements = np.sum((values1 == values2) & wall_mask)
            wall_agreement = wall_agreements / np.sum(wall_mask)
        
        path_agreement = 0.0
        if np.sum(path_mask) > 0:
            path_agreements = np.sum((values1 == values2) & path_mask)
            path_agreement = path_agreements / np.sum(path_mask)
        
        return {
            'overlap_size': total_overlap,
            'agreement_rate': agreement_rate,
            'wall_agreement': wall_agreement,
            'path_agreement': path_agreement
        }
    
    def calculate_layout_similarity_matrix(self, agent_states: List[AgentState]) -> np.ndarray:
        """
        计算多个智能体地图之间的相似性矩阵
        Args:
            agent_states: 智能体状态列表
        Returns:
            similarity_matrix: 相似性矩阵
        """
        n = len(agent_states)
        similarity_matrix = np.zeros((n, n))
        
        for i in range(n):
            for j in range(n):
                if i == j:
                    similarity_matrix[i, j] = 1.0
                else:
                    correlation = self.calculate_map_correlation(
                        agent_states[i].local_map, 
                        agent_states[j].local_map
                    )
                    similarity_matrix[i, j] = correlation
        
        return similarity_matrix
    
    def analyze_correlation_over_time(self, agent_states: List[AgentState]) -> Dict:
        """分析相关性随时间的变化"""
        n_agents = len(agent_states)
        
        if n_agents < 2:
            return {
                'current_correlation_matrix': np.array([[1.0]]),
                'mean_correlation': 1.0,
                'max_correlation': 1.0,
                'min_correlation': 1.0,
                'jaccard_similarities': np.array([[1.0]]),
                'overlap_statistics': {}
            }
        
        # 计算相关性矩阵
        correlation_matrix = self.calculate_layout_similarity_matrix(agent_states)
        
        # 计算Jaccard相似性矩阵
        jaccard_matrix = np.zeros((n_agents, n_agents))
        for i in range(n_agents):
            for j in range(n_agents):
                if i == j:
                    jaccard_matrix[i, j] = 1.0
                else:
                    jaccard_sim = self.calculate_jaccard_similarity(
                        agent_states[i].local_map,
                        agent_states[j].local_map
                    )
                    jaccard_matrix[i, j] = jaccard_sim
        
        # 计算重叠统计
        overlap_stats = {}
        for i in range(n_agents):
            for j in range(i + 1, n_agents):
                pair_key = f"agent_{i}_vs_agent_{j}"
                overlap_stats[pair_key] = self.calculate_overlap_agreement(
                    agent_states[i].local_map,
                    agent_states[j].local_map
                )
        
        # 提取上三角矩阵的值（不包括对角线）
        upper_triangle_indices = np.triu_indices(n_agents, k=1)
        correlations = correlation_matrix[upper_triangle_indices]
        
        # 处理空数组的情况
        if len(correlations) == 0:
            mean_corr = max_corr = min_corr = 1.0
        else:
            mean_corr = np.mean(correlations)
            max_corr = np.max(correlations)
            min_corr = np.min(correlations)
        
        # 保存历史记录
        analysis_result = {
            'timestamp': len(self.correlation_history),
            'current_correlation_matrix': correlation_matrix.copy(),
            'mean_correlation': mean_corr,
            'max_correlation': max_corr,
            'min_correlation': min_corr,
            'jaccard_similarities': jaccard_matrix,
            'overlap_statistics': overlap_stats
        }
        
        self.correlation_history.append(analysis_result)
        
        return analysis_result
    
    def get_correlation_trends(self) -> Dict:
        """获取相关性趋势"""
        if not self.correlation_history:
            return {
                'mean_trend': [],
                'max_trend': [],
                'min_trend': [],
                'volatility': 0.0
            }
        
        mean_trend = [entry['mean_correlation'] for entry in self.correlation_history]
        max_trend = [entry['max_correlation'] for entry in self.correlation_history]
        min_trend = [entry['min_correlation'] for entry in self.correlation_history]
        
        # 计算波动性（标准差）
        volatility = np.std(mean_trend) if len(mean_trend) > 1 else 0.0
        
        return {
            'mean_trend': mean_trend,
            'max_trend': max_trend,
            'min_trend': min_trend,
            'volatility': volatility
        }
    
    def calculate_exploration_complementarity(self, agent_states: List[AgentState]) -> Dict:
        """
        计算智能体探索的互补性
        Args:
            agent_states: 智能体状态列表
        Returns:
            Dict: 互补性统计
        """
        if len(agent_states) < 2:
            return {'complementarity_score': 0.0, 'unique_coverage': {}}
        
        # 计算每个智能体独有的探索区域
        unique_coverage = {}
        total_unique = 0
        
        for i, state in enumerate(agent_states):
            agent_known = state.local_map != -1
            
            # 计算其他智能体的联合已知区域
            others_known = np.zeros_like(agent_known, dtype=bool)
            for j, other_state in enumerate(agent_states):
                if i != j:
                    others_known |= (other_state.local_map != -1)
            
            # 计算当前智能体独有的区域
            unique_to_agent = agent_known & (~others_known)
            unique_count = np.sum(unique_to_agent)
            
            unique_coverage[f'agent_{i}'] = unique_count
            total_unique += unique_count
        
        # 计算总的已知区域
        total_known = np.zeros_like(agent_states[0].local_map, dtype=bool)
        for state in agent_states:
            total_known |= (state.local_map != -1)
        
        total_known_count = np.sum(total_known)
        
        # 互补性分数：独有区域占总已知区域的比例
        complementarity_score = total_unique / total_known_count if total_known_count > 0 else 0.0
        
        return {
            'complementarity_score': complementarity_score,
            'unique_coverage': unique_coverage,
            'total_unique': total_unique,
            'total_known': total_known_count
        }
    
    def visualize_correlation_matrix(self, correlation_matrix: np.ndarray, title: str = "Correlation Matrix"):
        """可视化相关性矩阵"""
        import matplotlib.pyplot as plt
        
        fig, ax = plt.subplots(figsize=(8, 6))
        
        im = ax.imshow(correlation_matrix, cmap='coolwarm', vmin=-1, vmax=1)
        
        # 添加文本标注
        n = correlation_matrix.shape[0]
        for i in range(n):
            for j in range(n):
                text = ax.text(j, i, f'{correlation_matrix[i, j]:.3f}',
                             ha="center", va="center", color="black")
        
        ax.set_title(title)
        ax.set_xlabel('Agent ID')
        ax.set_ylabel('Agent ID')
        
        # 设置刻度
        ax.set_xticks(range(n))
        ax.set_yticks(range(n))
        ax.set_xticklabels([f'Agent {i}' for i in range(n)])
        ax.set_yticklabels([f'Agent {i}' for i in range(n)])
        
        plt.colorbar(im, ax=ax, label='Correlation Coefficient')
        plt.tight_layout()
        plt.show()
    
    def export_correlation_data(self, filename: str = "correlation_analysis.json"):
        """导出相关性分析数据"""
        import json
        
        def safe_convert(obj):
            if isinstance(obj, np.ndarray):
                return obj.tolist()
            elif isinstance(obj, np.integer):
                return int(obj)
            elif isinstance(obj, np.floating):
                return float(obj)
            else:
                return obj
        
        export_data = {
            'correlation_history': [],
            'trends': self.get_correlation_trends()
        }
        
        for entry in self.correlation_history:
            converted_entry = {}
            for key, value in entry.items():
                converted_entry[key] = safe_convert(value)
            export_data['correlation_history'].append(converted_entry)
        
        # 转换趋势数据
        for key, value in export_data['trends'].items():
            export_data['trends'][key] = safe_convert(value)
        
        try:
            with open(filename, 'w') as f:
                json.dump(export_data, f, indent=2)
            print(f"相关性分析数据已导出到: {filename}")
        except Exception as e:
            print(f"导出相关性数据时出错: {e}")


if __name__ == "__main__":
    # 测试相关性分析器
    from agent_manager import AgentState
    import matplotlib.pyplot as plt
    
    # 创建测试数据
    maze_size = 10
    
    # 模拟3个智能体的地图观察
    agent_states = []
    
    for i in range(3):
        # 创建不同的局部地图
        local_map = np.full((maze_size, maze_size), -1, dtype=np.int8)
        
        # 智能体0：左上角区域
        if i == 0:
            local_map[0:5, 0:5] = np.random.choice([0, 1], size=(5, 5), p=[0.3, 0.7])
        
        # 智能体1：右上角区域（与智能体0有重叠）
        elif i == 1:
            local_map[0:5, 3:8] = np.random.choice([0, 1], size=(5, 5), p=[0.3, 0.7])
        
        # 智能体2：下方区域
        else:
            local_map[5:10, 2:7] = np.random.choice([0, 1], size=(5, 5), p=[0.3, 0.7])
        
        agent_state = AgentState(
            agent_id=i,
            position=np.array([i*2 + 2, i*2 + 2]),
            direction=np.array([1.0, 0.0]),
            sector=i,
            local_map=local_map,
            fused_map=local_map.copy(),
            exploration_area=0.0,
            reward_score=0.0,
            step_count=0
        )
        agent_states.append(agent_state)
    
    # 创建相关性分析器
    analyzer = CorrelationAnalyzer()
    
    # 进行分析
    analysis = analyzer.analyze_correlation_over_time(agent_states)
    
    print("相关性分析结果:")
    print(f"平均相关性: {analysis['mean_correlation']:.3f}")
    print(f"最大相关性: {analysis['max_correlation']:.3f}")
    print(f"最小相关性: {analysis['min_correlation']:.3f}")
    
    # 可视化相关性矩阵
    analyzer.visualize_correlation_matrix(analysis['current_correlation_matrix'])
    
    # 计算互补性
    complementarity = analyzer.calculate_exploration_complementarity(agent_states)
    print(f"\n互补性分析:")
    print(f"互补性分数: {complementarity['complementarity_score']:.3f}")
    print(f"独有覆盖: {complementarity['unique_coverage']}")
    
    # 显示重叠统计
    print(f"\n重叠统计:")
    for pair, stats in analysis['overlap_statistics'].items():
        print(f"{pair}:")
        print(f"  重叠大小: {stats['overlap_size']}")
        print(f"  一致率: {stats['agreement_rate']:.3f}")
        print(f"  墙壁一致率: {stats['wall_agreement']:.3f}")
        print(f"  通路一致率: {stats['path_agreement']:.3f}")
