#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SentryNet集成测试
SentryNet Integration Test

将训练好的SentryNet集成到现有的多智能体系统中，
测试其在实际环境中的表现，验证社会监控功能。
"""

import torch
import numpy as np
import matplotlib.pyplot as plt
from typing import Dict, List, Tuple, Optional
import os
from dataclasses import dataclass
from collections import deque

from sentry_net import SentryNet, SentryNetConfig
from sentry_data_collector import PeerObservationExtractor, DataCollectionConfig
from agent_manager import AgentManager, AgentState
from maze_generator import RoomBasedMazeGenerator
from navigation_strategies import ExplorationNavigationStrategy
from video_recorder import VideoRecorder


@dataclass
class IntegrationTestConfig:
    """集成测试配置"""
    model_path: str = "sentry_models/best_model.pth"
    maze_size: int = 15
    num_agents: int = 3
    test_episodes: int = 5
    steps_per_episode: int = 200
    
    # 社会监控参数
    boundary_threshold: float = 2.0
    warning_threshold: float = 0.7  # SPC激活阈值，超过此值发送警告
    social_penalty: float = -0.5  # 社会惩罚
    
    # 可视化
    record_video: bool = True
    save_results: bool = True
    results_dir: str = "sentry_test_results"


class SocialAgent:
    """集成了SentryNet的社会智能体"""
    
    def __init__(self, agent_id: int, sentry_net: SentryNet, config: IntegrationTestConfig):
        self.agent_id = agent_id
        self.sentry_net = sentry_net
        self.config = config
        
        # 观察提取器
        collection_config = DataCollectionConfig(maze_size=config.maze_size)
        self.extractor = PeerObservationExtractor(collection_config)
        
        # 导航策略
        self.navigation_strategy = ExplorationNavigationStrategy()
        
        # 社会监控状态
        self.peer_hidden_states = {}  # 每个同伴的隐藏状态
        self.peer_position_histories = {}  # 同伴位置历史
        self.warning_sent = {}  # 是否已发送警告
        self.social_penalty_received = 0.0  # 接收到的社会惩罚
        
        # 统计信息
        self.warnings_sent_count = 0
        self.warnings_received_count = 0
        self.boundary_violations = 0
    
    def reset_episode(self):
        """重置episode状态"""
        self.peer_hidden_states.clear()
        self.peer_position_histories.clear()
        self.warning_sent.clear()
        self.social_penalty_received = 0.0
        self.warnings_sent_count = 0
        self.warnings_received_count = 0
        self.boundary_violations = 0
    
    def observe_peer(self, peer_state: AgentState, ground_truth_maze: np.ndarray) -> Dict:
        """
        观察同伴并生成社会监控信息
        Args:
            peer_state: 同伴状态
            ground_truth_maze: 真实地图
        Returns:
            social_info: 社会监控信息
        """
        peer_id = peer_state.agent_id
        
        # 初始化同伴状态
        if peer_id not in self.peer_hidden_states:
            self.peer_hidden_states[peer_id] = self.sentry_net.reset_hidden_state(1)
            self.peer_position_histories[peer_id] = deque(maxlen=5)
            self.warning_sent[peer_id] = False
        
        # 更新位置历史
        self.peer_position_histories[peer_id].append(peer_state.position.copy())
        
        # 提取观察数据
        peer_patch = self.extractor.extract_peer_local_patch(peer_state, ground_truth_maze)
        peer_velocity = self.extractor.calculate_peer_velocity(self.peer_position_histories[peer_id])
        
        # 转换为tensor
        patch_tensor = torch.FloatTensor(peer_patch).unsqueeze(0)
        velocity_tensor = torch.FloatTensor(peer_velocity).unsqueeze(0)
        
        # SentryNet前向传播
        with torch.no_grad():
            outputs = self.sentry_net(
                patch_tensor, velocity_tensor, 
                self.peer_hidden_states[peer_id]
            )
            
            attention_key = outputs['attention_key'].squeeze(0).numpy()
            message_value = outputs['message_value'].squeeze(0).numpy()
            self.peer_hidden_states[peer_id] = outputs['new_hidden_state']
        
        # 计算威胁等级（SPC激活强度）
        threat_level = np.mean(attention_key)  # 简化版本，实际可以更复杂
        
        # 判断是否需要发送警告
        should_warn = (threat_level > self.config.warning_threshold and 
                      not self.warning_sent[peer_id])
        
        if should_warn:
            self.warning_sent[peer_id] = True
            self.warnings_sent_count += 1
        
        return {
            'peer_id': peer_id,
            'threat_level': threat_level,
            'attention_key': attention_key,
            'message_value': message_value,
            'should_warn': should_warn,
            'peer_position': peer_state.position.copy()
        }
    
    def receive_warning(self, warning_info: Dict) -> bool:
        """
        接收警告并检查自身状态
        Args:
            warning_info: 警告信息
        Returns:
            bool: 是否确实需要调整行为
        """
        self.warnings_received_count += 1
        
        # 简化版本：如果接收到警告，检查自身是否真的靠近边界
        # 实际实现中可以更复杂
        return True
    
    def apply_social_penalty(self, penalty: float):
        """应用社会惩罚"""
        self.social_penalty_received += penalty
    
    def is_near_boundary(self, position: np.ndarray, sector: int) -> bool:
        """判断是否靠近边界（简化版本）"""
        x, y = position
        
        # 检查迷宫边界
        if (x < self.config.boundary_threshold or 
            x > self.config.maze_size - self.config.boundary_threshold or
            y < self.config.boundary_threshold or 
            y > self.config.maze_size - self.config.boundary_threshold):
            return True
        
        # 检查扇区边界（简化）
        center = self.config.maze_size // 2
        if sector == 0 and (x > center - self.config.boundary_threshold or 
                           y > center - self.config.boundary_threshold):
            return True
        elif sector == 1 and (x < center + self.config.boundary_threshold or 
                             y > center - self.config.boundary_threshold):
            return True
        elif sector == 2 and (x > center - self.config.boundary_threshold or 
                             y < center + self.config.boundary_threshold):
            return True
        
        return False


class SentryIntegrationTester:
    """SentryNet集成测试器"""
    
    def __init__(self, config: IntegrationTestConfig):
        self.config = config
        
        # 加载训练好的模型
        self.sentry_net = self._load_model()
        
        # 创建结果目录
        if config.save_results:
            os.makedirs(config.results_dir, exist_ok=True)
        
        # 测试结果存储
        self.test_results = []
    
    def _load_model(self) -> SentryNet:
        """加载训练好的SentryNet模型"""
        if not os.path.exists(self.config.model_path):
            raise FileNotFoundError(f"模型文件不存在: {self.config.model_path}")
        
        checkpoint = torch.load(self.config.model_path, map_location='cpu')
        sentry_config = checkpoint['sentry_config']
        
        model = SentryNet(sentry_config)
        model.load_state_dict(checkpoint['model_state_dict'])
        model.eval()
        
        print(f"SentryNet模型已从 {self.config.model_path} 加载")
        return model
    
    def run_test_episode(self, episode_id: int) -> Dict:
        """运行单个测试episode"""
        print(f"运行测试Episode {episode_id + 1}/{self.config.test_episodes}")
        
        # 生成测试迷宫
        maze_generator = RoomBasedMazeGenerator(self.config.maze_size)
        maze = maze_generator.generate_room_based_maze()
        
        # 初始化智能体管理器
        agent_manager = AgentManager(self.config.maze_size, self.config.num_agents)
        agent_manager.initialize_agents(maze)
        
        # 创建社会智能体
        social_agents = {}
        for i in range(self.config.num_agents):
            social_agents[i] = SocialAgent(i, self.sentry_net, self.config)
            social_agents[i].reset_episode()
        
        # 视频录制
        video_recorder = None
        if self.config.record_video:
            video_filename = os.path.join(
                self.config.results_dir, 
                f"sentry_test_episode_{episode_id}.gif"
            )
            video_recorder = VideoRecorder(video_filename)
        
        # Episode统计
        episode_stats = {
            'total_warnings': 0,
            'total_penalties': 0,
            'boundary_violations': 0,
            'agent_stats': {i: {} for i in range(self.config.num_agents)}
        }
        
        # 仿真步骤
        for step in range(self.config.steps_per_episode):
            step_warnings = []
            
            # 每个智能体观察其他智能体
            for observer_id in range(self.config.num_agents):
                observer_agent = social_agents[observer_id]
                observer_state = agent_manager.agent_states[observer_id]
                
                for peer_id in range(self.config.num_agents):
                    if peer_id == observer_id:
                        continue
                    
                    peer_state = agent_manager.agent_states[peer_id]
                    
                    # 观察同伴
                    social_info = observer_agent.observe_peer(peer_state, maze)
                    
                    # 如果需要发送警告
                    if social_info['should_warn']:
                        step_warnings.append({
                            'sender': observer_id,
                            'target': peer_id,
                            'threat_level': social_info['threat_level'],
                            'step': step
                        })
            
            # 处理警告和惩罚
            for warning in step_warnings:
                target_agent = social_agents[warning['target']]
                target_state = agent_manager.agent_states[warning['target']]
                
                # 发送警告
                needs_adjustment = target_agent.receive_warning(warning)
                
                # 检查是否真的违规
                if target_agent.is_near_boundary(target_state.position, target_state.sector):
                    target_agent.boundary_violations += 1
                    episode_stats['boundary_violations'] += 1
                    
                    # 应用社会惩罚
                    target_agent.apply_social_penalty(self.config.social_penalty)
                    episode_stats['total_penalties'] += 1
                
                episode_stats['total_warnings'] += 1
            
            # 移动智能体
            for i, agent_state in enumerate(agent_manager.agent_states):
                social_agent = social_agents[i]
                
                # 使用导航策略选择移动
                new_pos, new_dir = social_agent.navigation_strategy.select_next_move(
                    agent_state, maze
                )
                
                agent_state.position = new_pos
                agent_state.direction = new_dir
                agent_state.step_count += 1
            
            # 录制视频帧
            if video_recorder:
                video_recorder.add_frame(agent_manager.agent_states, maze, step)
        
        # 收集episode统计
        for i, social_agent in social_agents.items():
            episode_stats['agent_stats'][i] = {
                'warnings_sent': social_agent.warnings_sent_count,
                'warnings_received': social_agent.warnings_received_count,
                'boundary_violations': social_agent.boundary_violations,
                'social_penalty': social_agent.social_penalty_received
            }
        
        # 保存视频
        if video_recorder:
            video_recorder.save()
            print(f"视频已保存: {video_filename}")
        
        return episode_stats
    
    def run_all_tests(self) -> Dict:
        """运行所有测试"""
        print("开始SentryNet集成测试...")
        print(f"配置: {self.config.test_episodes} episodes, {self.config.steps_per_episode} steps/episode")
        
        all_episode_stats = []
        
        for episode_id in range(self.config.test_episodes):
            episode_stats = self.run_test_episode(episode_id)
            all_episode_stats.append(episode_stats)
            
            print(f"Episode {episode_id + 1} 完成:")
            print(f"  警告数: {episode_stats['total_warnings']}")
            print(f"  惩罚数: {episode_stats['total_penalties']}")
            print(f"  边界违规: {episode_stats['boundary_violations']}")
        
        # 汇总统计
        summary_stats = self._compute_summary_stats(all_episode_stats)
        
        # 可视化结果
        self._visualize_results(all_episode_stats, summary_stats)
        
        # 保存结果
        if self.config.save_results:
            self._save_results(all_episode_stats, summary_stats)
        
        return {
            'episode_stats': all_episode_stats,
            'summary_stats': summary_stats
        }
    
    def _compute_summary_stats(self, all_episode_stats: List[Dict]) -> Dict:
        """计算汇总统计"""
        total_warnings = sum(ep['total_warnings'] for ep in all_episode_stats)
        total_penalties = sum(ep['total_penalties'] for ep in all_episode_stats)
        total_violations = sum(ep['boundary_violations'] for ep in all_episode_stats)
        
        # 按智能体统计
        agent_summary = {}
        for agent_id in range(self.config.num_agents):
            agent_summary[agent_id] = {
                'total_warnings_sent': sum(ep['agent_stats'][agent_id]['warnings_sent'] 
                                         for ep in all_episode_stats),
                'total_warnings_received': sum(ep['agent_stats'][agent_id]['warnings_received'] 
                                             for ep in all_episode_stats),
                'total_violations': sum(ep['agent_stats'][agent_id]['boundary_violations'] 
                                      for ep in all_episode_stats),
                'total_penalty': sum(ep['agent_stats'][agent_id]['social_penalty'] 
                                   for ep in all_episode_stats)
            }
        
        return {
            'total_episodes': len(all_episode_stats),
            'total_warnings': total_warnings,
            'total_penalties': total_penalties,
            'total_violations': total_violations,
            'avg_warnings_per_episode': total_warnings / len(all_episode_stats),
            'avg_penalties_per_episode': total_penalties / len(all_episode_stats),
            'avg_violations_per_episode': total_violations / len(all_episode_stats),
            'warning_effectiveness': total_penalties / max(total_warnings, 1),
            'agent_summary': agent_summary
        }
    
    def _visualize_results(self, all_episode_stats: List[Dict], summary_stats: Dict):
        """可视化测试结果"""
        fig, axes = plt.subplots(2, 2, figsize=(12, 10))
        
        # 1. 每个episode的统计
        episodes = range(1, len(all_episode_stats) + 1)
        warnings = [ep['total_warnings'] for ep in all_episode_stats]
        penalties = [ep['total_penalties'] for ep in all_episode_stats]
        violations = [ep['boundary_violations'] for ep in all_episode_stats]
        
        axes[0, 0].plot(episodes, warnings, 'b-o', label='Warnings')
        axes[0, 0].plot(episodes, penalties, 'r-s', label='Penalties')
        axes[0, 0].plot(episodes, violations, 'g-^', label='Violations')
        axes[0, 0].set_xlabel('Episode')
        axes[0, 0].set_ylabel('Count')
        axes[0, 0].set_title('Episode Statistics')
        axes[0, 0].legend()
        axes[0, 0].grid(True)
        
        # 2. 智能体对比
        agent_ids = list(range(self.config.num_agents))
        warnings_sent = [summary_stats['agent_summary'][i]['total_warnings_sent'] 
                        for i in agent_ids]
        warnings_received = [summary_stats['agent_summary'][i]['total_warnings_received'] 
                           for i in agent_ids]
        
        x = np.arange(len(agent_ids))
        width = 0.35
        
        axes[0, 1].bar(x - width/2, warnings_sent, width, label='Sent', alpha=0.7)
        axes[0, 1].bar(x + width/2, warnings_received, width, label='Received', alpha=0.7)
        axes[0, 1].set_xlabel('Agent ID')
        axes[0, 1].set_ylabel('Warning Count')
        axes[0, 1].set_title('Warnings by Agent')
        axes[0, 1].set_xticks(x)
        axes[0, 1].set_xticklabels(agent_ids)
        axes[0, 1].legend()
        
        # 3. 违规和惩罚
        violations_by_agent = [summary_stats['agent_summary'][i]['total_violations'] 
                              for i in agent_ids]
        penalties_by_agent = [abs(summary_stats['agent_summary'][i]['total_penalty']) 
                             for i in agent_ids]
        
        axes[1, 0].bar(x - width/2, violations_by_agent, width, label='Violations', alpha=0.7)
        axes[1, 0].bar(x + width/2, penalties_by_agent, width, label='Penalties', alpha=0.7)
        axes[1, 0].set_xlabel('Agent ID')
        axes[1, 0].set_ylabel('Count')
        axes[1, 0].set_title('Violations and Penalties')
        axes[1, 0].set_xticks(x)
        axes[1, 0].set_xticklabels(agent_ids)
        axes[1, 0].legend()
        
        # 4. 汇总指标
        metrics = ['Avg Warnings', 'Avg Penalties', 'Avg Violations', 'Effectiveness']
        values = [
            summary_stats['avg_warnings_per_episode'],
            summary_stats['avg_penalties_per_episode'],
            summary_stats['avg_violations_per_episode'],
            summary_stats['warning_effectiveness']
        ]
        
        axes[1, 1].bar(metrics, values, alpha=0.7)
        axes[1, 1].set_ylabel('Value')
        axes[1, 1].set_title('Summary Metrics')
        axes[1, 1].tick_params(axis='x', rotation=45)
        
        plt.tight_layout()
        
        if self.config.save_results:
            plt.savefig(os.path.join(self.config.results_dir, 'integration_test_results.png'),
                       dpi=300, bbox_inches='tight')
        
        plt.show()
    
    def _save_results(self, all_episode_stats: List[Dict], summary_stats: Dict):
        """保存测试结果"""
        import json
        
        results = {
            'config': {
                'test_episodes': self.config.test_episodes,
                'steps_per_episode': self.config.steps_per_episode,
                'warning_threshold': self.config.warning_threshold,
                'social_penalty': self.config.social_penalty
            },
            'episode_stats': all_episode_stats,
            'summary_stats': summary_stats
        }
        
        results_file = os.path.join(self.config.results_dir, 'integration_test_results.json')
        with open(results_file, 'w') as f:
            json.dump(results, f, indent=2)
        
        print(f"测试结果已保存到: {results_file}")


def main():
    """主函数"""
    config = IntegrationTestConfig()
    
    # 检查模型是否存在
    if not os.path.exists(config.model_path):
        print(f"错误: 模型文件不存在 {config.model_path}")
        print("请先运行 sentry_trainer.py 训练模型")
        return
    
    # 创建测试器
    tester = SentryIntegrationTester(config)
    
    # 运行测试
    results = tester.run_all_tests()
    
    # 打印汇总报告
    print("\n=== SentryNet集成测试报告 ===")
    summary = results['summary_stats']
    print(f"总测试episodes: {summary['total_episodes']}")
    print(f"总警告数: {summary['total_warnings']}")
    print(f"总惩罚数: {summary['total_penalties']}")
    print(f"总违规数: {summary['total_violations']}")
    print(f"平均每episode警告数: {summary['avg_warnings_per_episode']:.2f}")
    print(f"警告有效性: {summary['warning_effectiveness']:.2f}")
    
    print("\n按智能体统计:")
    for agent_id, stats in summary['agent_summary'].items():
        print(f"  Agent {agent_id}:")
        print(f"    发送警告: {stats['total_warnings_sent']}")
        print(f"    接收警告: {stats['total_warnings_received']}")
        print(f"    边界违规: {stats['total_violations']}")
        print(f"    社会惩罚: {stats['total_penalty']:.2f}")


if __name__ == "__main__":
    main()
