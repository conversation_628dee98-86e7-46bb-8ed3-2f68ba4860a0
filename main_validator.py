# -*- coding: utf-8 -*-
# File: main_validator.py
#!/usr/bin/env python3
"""
多智能体协同SLAM验证框架 - 主模块
Multi-Agent Collaborative SLAM Validation Framework - Main Module
"""

import numpy as np
import matplotlib.pyplot as plt
import matplotlib.animation as animation
import cv2
from typing import Dict, List, Tuple, Optional
import random
import time
import json
import os
from dataclasses import dataclass

from maze_generator import RoomBasedMazeGenerator
from agent_manager import AgentManager, AgentState
from fov_simulator import FOVSimulator
from correlation_analyzer import CorrelationAnalyzer
from reward_calculator import RewardCalculator
from video_recorder import VideoRecorder
from navigation_strategies import RandomNavigationStrategy, ExplorationNavigationStrategy, WallFollowingStrategy, FrontierExplorationStrategy


class MultiAgentSLAMValidator:
    """多智能体SLAM验证器 - 主要验证框架"""
    
    def __init__(self, maze_size: int = 15, num_agents: int = 3, seed: Optional[int] = None,
                 use_dataset_maze: bool = False, dataset_path: Optional[str] = None):
        self.maze_size = maze_size
        self.num_agents = num_agents
        self.use_dataset_maze = use_dataset_maze

        if seed is not None:
            np.random.seed(seed)
            random.seed(seed)

        # 初始化组件
        self.maze_generator = RoomBasedMazeGenerator(maze_size, seed, dataset_path)
        self.agent_manager = AgentManager(maze_size, num_agents)
        self.fov_simulator = FOVSimulator(maze_size, fov_angle=75)
        self.correlation_analyzer = CorrelationAnalyzer()
        self.reward_calculator = RewardCalculator(self.agent_manager.sector_manager)
        self.video_recorder = VideoRecorder(maze_size)

        # 状态管理
        self.ground_truth_maze: Optional[np.ndarray] = None
        self.maze_config: dict = {}
        self.step_count = 0

        # 结果记录
        self.results = {
            'rewards_history': [],
            'correlation_history': [],
            'exploration_progress': [],
            'sector_violations': [],
            'agent_positions_history': []
        }
    
    def initialize_simulation(self, navigation_strategy, maze_complexity: float = 0.7) -> None:
        """初始化仿真"""
        # 根据设置选择迷宫生成方式
        if self.use_dataset_maze:
            # 从数据集随机加载迷宫
            self.ground_truth_maze, self.maze_config = self.maze_generator.load_random_dataset_maze()
            print(f"使用数据集迷宫，配置: {self.maze_config.get('env_name', 'Unknown')}")
        else:
            # 生成房间式迷宫
            self.ground_truth_maze = self.maze_generator.generate_room_based_maze(
                num_rooms=random.randint(4, 5),
                complexity=maze_complexity
            )
            self.maze_config = {}
            print("使用生成的房间式迷宫")

        # 初始化智能体（确保在通路上且不穿墙）
        self.agent_manager.initialize_agents(self.ground_truth_maze)
        
        # 初始化FOV观察和SLAM地图
        for agent_state in self.agent_manager.agent_states:
            # 获取初始FOV观察
            agent_state.local_map = self.fov_simulator.get_fov_observation(
                agent_state.position, agent_state.direction, self.ground_truth_maze
            )
            # 初始化SLAM地图为第一次观察
            agent_state.slam_map = agent_state.local_map.copy()
        
        self.navigation_strategy = navigation_strategy
        self.step_count = 0
        
        # 初始化视频录制
        self.video_recorder.initialize_recording()
        
        print(f"仿真初始化完成: {self.num_agents}个智能体, 迷宫大小{self.maze_size}x{self.maze_size}")
        print(f"导航策略: {navigation_strategy.get_name()}")
    
    def run_simulation(self, max_steps: int = 200, record_video: bool = True) -> Dict:
        """运行仿真"""
        print(f"开始仿真，最大步数: {max_steps}")
        
        for step in range(max_steps):
            self.step_count = step
            
            # 更新智能体位置（不允许穿墙）
            for agent_state in self.agent_manager.agent_states:
                new_position, new_direction = self.navigation_strategy.select_next_move(
                    agent_state, self.ground_truth_maze
                )
                
                # 验证新位置不穿墙
                if self._is_valid_position(new_position):
                    agent_state.position = new_position
                    agent_state.direction = new_direction
                agent_state.step_count += 1
            
            # 更新FOV观察和SLAM地图
            for agent_state in self.agent_manager.agent_states:
                # 获取当前FOV观察
                agent_state.local_map = self.fov_simulator.get_fov_observation(
                    agent_state.position, agent_state.direction, self.ground_truth_maze
                )
                # 更新累积SLAM地图
                agent_state.slam_map = self.fov_simulator.update_slam_map(
                    agent_state.slam_map, agent_state.position, agent_state.direction, self.ground_truth_maze
                )
            
            # 地图融合（整张地图相似性计算）
            self.agent_manager.fuse_all_maps()
            
            # 计算奖励
            step_rewards = []
            sector_violations = 0
            for agent_state in self.agent_manager.agent_states:
                reward = self.reward_calculator.calculate_reward(
                    agent_state, self.agent_manager.agent_states
                )
                agent_state.reward_score += reward
                step_rewards.append(reward)
                
                # 统计区域违规
                if not self.agent_manager.sector_manager.is_in_own_sector(
                    agent_state.agent_id, agent_state.position
                ):
                    sector_violations += 1
            
            # 相关性分析（只计算已感知区域）
            correlation_analysis = self.correlation_analyzer.analyze_correlation_over_time(
                self.agent_manager.agent_states
            )
            
            # 记录位置历史
            positions = [(state.position.copy(), state.direction.copy()) 
                        for state in self.agent_manager.agent_states]
            self.results['agent_positions_history'].append(positions)
            
            # 记录结果
            self.results['rewards_history'].append(step_rewards)
            self.results['correlation_history'].append(correlation_analysis)
            self.results['sector_violations'].append(sector_violations)
            
            # 计算探索进度
            exploration_progress = self._calculate_exploration_progress()
            self.results['exploration_progress'].append(exploration_progress)
            
            # 录制视频帧
            if record_video:
                self.video_recorder.add_frame(
                    self.ground_truth_maze,
                    self.agent_manager.agent_states,
                    self.agent_manager.sector_manager.sectors,
                    step
                )
            
            # 每50步打印进度
            if step % 50 == 0:
                mean_correlation = correlation_analysis['mean_correlation']
                total_reward = sum(state.reward_score for state in self.agent_manager.agent_states)
                print(f"Step {step}: 探索进度={exploration_progress:.3f}, "
                      f"平均相关性={mean_correlation:.3f}, 总奖励={total_reward:.2f}, "
                      f"区域违规={sector_violations}")
        
        # 保存视频
        if record_video:
            video_path = f"simulation_{self.navigation_strategy.get_name().lower()}_{int(time.time())}.mp4"
            self.video_recorder.save_video(video_path)
            print(f"仿真视频已保存: {video_path}")
        
        print("仿真完成")
        return self.results
    
    def _is_valid_position(self, position: np.ndarray) -> bool:
        """检查位置是否有效（不穿墙，在边界内）"""
        x, y = position[0], position[1]
        
        # 边界检查
        if x < 0.5 or x >= self.maze_size - 0.5 or y < 0.5 or y >= self.maze_size - 0.5:
            return False
        
        # 墙壁检查
        grid_x, grid_y = int(x), int(y)
        if self.ground_truth_maze[grid_y, grid_x] == 0:  # 0为墙壁
            return False
        
        return True
    
    def _calculate_exploration_progress(self) -> float:
        """计算探索进度"""
        # 使用融合地图计算
        fused_map = self.agent_manager.get_global_fused_map()
        total_known_unique = np.sum(fused_map != -1)
        total_possible = np.sum(self.ground_truth_maze == 1)  # 只考虑通路区域
        
        return total_known_unique / total_possible if total_possible > 0 else 0.0
    
    def visualize_results(self, save_path: str = "validation_results.png") -> None:
        """可视化结果"""
        fig, axes = plt.subplots(2, 4, figsize=(24, 12))
        
        # 1. 迷宫和智能体轨迹
        axes[0, 0].imshow(self.ground_truth_maze, cmap='gray', origin='lower')
        colors = ['red', 'blue', 'green', 'orange', 'purple']
        
        # 绘制轨迹
        for agent_idx in range(self.num_agents):
            if len(self.results['agent_positions_history']) > 0:
                trajectory_x = [pos[agent_idx][0][0] for pos in self.results['agent_positions_history'] 
                               if agent_idx < len(pos)]
                trajectory_y = [pos[agent_idx][0][1] for pos in self.results['agent_positions_history'] 
                               if agent_idx < len(pos)]
                if trajectory_x and trajectory_y:
                    axes[0, 0].plot(trajectory_x, trajectory_y, '-', 
                                   color=colors[agent_idx % len(colors)], alpha=0.7, linewidth=2)
                    # 起始点和结束点
                    axes[0, 0].plot(trajectory_x[0], trajectory_y[0], 'o', 
                                   color=colors[agent_idx % len(colors)], markersize=8)
                    axes[0, 0].plot(trajectory_x[-1], trajectory_y[-1], 's', 
                                   color=colors[agent_idx % len(colors)], markersize=8)
        
        axes[0, 0].set_title('Ground Truth Maze & Agent Trajectories')
        axes[0, 0].legend([f'Agent {i}' for i in range(self.num_agents)])
        
        # 2. 扇区分割
        sector_viz = np.zeros((self.maze_size, self.maze_size, 3))
        colors_rgb = [(1,0,0), (0,1,0), (0,0,1), (1,1,0), (1,0,1)]
        for i, sector_mask in enumerate(self.agent_manager.sector_manager.sectors):
            sector_viz[sector_mask] = colors_rgb[i % len(colors_rgb)]
        axes[0, 1].imshow(sector_viz, origin='lower')
        axes[0, 1].set_title('Sector Division')
        
        # 3. 奖励历史
        if self.results['rewards_history']:
            rewards_array = np.array(self.results['rewards_history'])
            for i in range(min(self.num_agents, rewards_array.shape[1])):
                axes[0, 2].plot(rewards_array[:, i], label=f'Agent {i}')
            axes[0, 2].set_title('Reward History')
            axes[0, 2].set_xlabel('Steps')
            axes[0, 2].set_ylabel('Reward')
            axes[0, 2].legend()
            axes[0, 2].grid(True)
        
        # 4. 相关性变化
        if self.results['correlation_history']:
            correlations = [item['mean_correlation'] for item in self.results['correlation_history']]
            axes[1, 0].plot(correlations, 'b-', linewidth=2)
            axes[1, 0].set_title('Layout Correlation Over Time')
            axes[1, 0].set_xlabel('Steps')
            axes[1, 0].set_ylabel('Mean Correlation')
            axes[1, 0].grid(True)
        
        # 5. 探索进度
        if self.results['exploration_progress']:
            axes[1, 1].plot(self.results['exploration_progress'], 'g-', linewidth=2)
            axes[1, 1].set_title('Exploration Progress')
            axes[1, 1].set_xlabel('Steps')
            axes[1, 1].set_ylabel('Progress')
            axes[1, 1].grid(True)
        
        # 6. 融合SLAM地图
        fused_slam_map = self.agent_manager.get_global_fused_map()
        display_fused = np.where(fused_slam_map == -1, 0.5, fused_slam_map)
        axes[0, 3].imshow(display_fused, cmap='gray', origin='lower', vmin=0, vmax=1)
        axes[0, 3].set_title('Fused SLAM Map')

        # 标记最终位置
        for i, agent_state in enumerate(self.agent_manager.agent_states):
            axes[0, 3].plot(agent_state.position[0], agent_state.position[1], 'o',
                           color=colors[i % len(colors)], markersize=8)

        # 7. 单个Agent SLAM地图（选择第一个agent）
        if self.agent_manager.agent_states:
            agent_slam = self.agent_manager.agent_states[0].slam_map
            display_agent = np.where(agent_slam == -1, 0.5, agent_slam)
            axes[1, 2].imshow(display_agent, cmap='gray', origin='lower', vmin=0, vmax=1)
            axes[1, 2].set_title(f'Agent 0 Individual SLAM')

            # 标记agent位置
            pos = self.agent_manager.agent_states[0].position
            axes[1, 2].plot(pos[0], pos[1], 'o', color=colors[0], markersize=8)

        # 8. 区域违规统计
        if self.results['sector_violations']:
            axes[1, 3].plot(self.results['sector_violations'], 'r-', linewidth=2)
            axes[1, 3].set_title('Sector Violations')
            axes[1, 3].set_xlabel('Steps')
            axes[1, 3].set_ylabel('Violations Count')
            axes[1, 3].grid(True)
        
        plt.tight_layout()
        plt.savefig(save_path, dpi=150, bbox_inches='tight')
        plt.close()
        print(f"结果可视化已保存: {save_path}")
    
    def generate_correlation_test_data(self, correlations: List[float], num_tests: int = 3) -> Dict:
        """生成不同相关性的测试数据"""
        test_results = {}
        
        base_maze = self.maze_generator.generate_room_based_maze()
        
        for correlation in correlations:
            print(f"\n测试相关性: {correlation}")
            correlation_results = []
            
            for test_idx in range(num_tests):
                # 生成相关迷宫
                if correlation == 1.0:
                    test_maze = base_maze.copy()
                else:
                    test_maze = self.maze_generator.generate_correlated_maze(base_maze, correlation)
                
                # 运行短期仿真测试
                original_maze = self.ground_truth_maze
                self.ground_truth_maze = test_maze
                
                # 重新初始化智能体
                self.initialize_simulation(self.navigation_strategy)
                results = self.run_simulation(50, record_video=False)  # 短期测试
                
                # 计算最终相关性
                if results['correlation_history']:
                    final_correlation_analysis = results['correlation_history'][-1]
                    measured_correlation = final_correlation_analysis['mean_correlation']
                else:
                    measured_correlation = 0.0
                
                correlation_results.append({
                    'theoretical_correlation': correlation,
                    'measured_correlation': measured_correlation,
                    'total_reward': sum(state.reward_score for state in self.agent_manager.agent_states),
                    'exploration_progress': results['exploration_progress'][-1] if results['exploration_progress'] else 0.0
                })
                
                self.ground_truth_maze = original_maze
            
            test_results[correlation] = correlation_results
        
        return test_results
    
    def export_results(self, filename: str = "simulation_results.json") -> None:
        """导出结果到JSON文件"""
        def safe_convert(obj):
            if isinstance(obj, np.ndarray):
                return obj.tolist()
            elif isinstance(obj, np.integer):
                return int(obj)
            elif isinstance(obj, np.floating):
                return float(obj)
            elif hasattr(obj, '__dict__'):
                return str(obj)
            else:
                return obj
        
        export_data = {
            'simulation_config': {
                'maze_size': self.maze_size,
                'num_agents': self.num_agents,
                'navigation_strategy': self.navigation_strategy.get_name() if hasattr(self, 'navigation_strategy') else 'Unknown',
                'total_steps': self.step_count,
                'use_dataset_maze': self.use_dataset_maze,
                'maze_source': 'dataset' if self.use_dataset_maze else 'generated',
                'maze_config': self.maze_config
            },
            'final_statistics': {
                'total_rewards': [float(state.reward_score) for state in self.agent_manager.agent_states],
                'final_positions': [state.position.tolist() for state in self.agent_manager.agent_states],
                'exploration_progress': float(self.results['exploration_progress'][-1]) if self.results['exploration_progress'] else 0.0,
                'final_correlation': safe_convert(self.results['correlation_history'][-1]) if self.results['correlation_history'] else None
            },
            'step_by_step_results': {
                'rewards_history': [[float(r) for r in step_rewards] for step_rewards in self.results['rewards_history']],
                'exploration_progress': [float(p) for p in self.results['exploration_progress']],
                'sector_violations': [int(v) for v in self.results['sector_violations']]
            }
        }
        
        try:
            with open(filename, 'w') as f:
                json.dump(export_data, f, indent=2)
            print(f"结果已导出到: {filename}")
        except Exception as e:
            print(f"导出结果时出错: {e}")


def run_validation_demo(use_dataset_maze: bool = False):
    """运行验证演示"""
    print("=" * 60)
    print("多智能体协同SLAM验证框架演示")
    print("=" * 60)

    # 创建验证器
    validator = MultiAgentSLAMValidator(maze_size=15, num_agents=3, seed=42, use_dataset_maze=use_dataset_maze)
    
    # 测试不同导航策略
    strategies = [
        RandomNavigationStrategy(), 
        ExplorationNavigationStrategy(),
        # WallFollowingStrategy(),
        # FrontierExplorationStrategy()
    ]
    
    for strategy in strategies:
        print(f"\n🧪 测试导航策略: {strategy.get_name()}")
        
        # 初始化仿真
        validator.initialize_simulation(strategy, maze_complexity=0.7)
        
        # 运行仿真（包含视频录制）
        results = validator.run_simulation(max_steps=150, record_video=True)
        
        # 可视化结果
        save_path = f"results_{strategy.get_name().lower()}.png"
        validator.visualize_results(save_path)
        
        # 导出结果
        json_path = f"results_{strategy.get_name().lower()}.json"
        validator.export_results(json_path)
        
        # 打印统计信息
        print(f"📊 最终统计:")
        print(f"   总奖励: {sum(state.reward_score for state in validator.agent_manager.agent_states):.2f}")
        print(f"   探索进度: {results['exploration_progress'][-1]:.3f}")
        if results['correlation_history']:
            final_corr = results['correlation_history'][-1]['mean_correlation']
            print(f"   最终平均相关性: {final_corr:.3f}")
        print(f"   区域违规总数: {sum(results['sector_violations'])}")
    
    # 相关性测试
    print(f"\n🔬 运行相关性测试...")
    correlations_to_test = [0.3, 0.6, 0.9]
    test_results = validator.generate_correlation_test_data(correlations_to_test, num_tests=2)
    
    # 打印相关性测试结果
    print(f"\n📈 相关性测试结果:")
    for corr, results in test_results.items():
        avg_measured = np.mean([r['measured_correlation'] for r in results])
        avg_reward = np.mean([r['total_reward'] for r in results])
        avg_progress = np.mean([r['exploration_progress'] for r in results])
        print(f"  理论相关性 {corr}: 实测={avg_measured:.3f}, 奖励={avg_reward:.1f}, 进度={avg_progress:.3f}")
    
    print(f"\n✅ 验证演示完成!")
    print(f"生成的文件:")
    print(f"  - results_*.png (策略结果可视化)")
    print(f"  - results_*.json (详细结果数据)")
    print(f"  - simulation_*.mp4 (仿真视频)")


def run_dataset_maze_demo():
    """运行数据集迷宫演示"""
    print("=" * 60)
    print("数据集迷宫加载演示")
    print("=" * 60)

    # 创建使用数据集迷宫的验证器
    validator = MultiAgentSLAMValidator(maze_size=15, num_agents=3, seed=42, use_dataset_maze=True)

    # 测试不同导航策略
    strategies = [
        RandomNavigationStrategy(),
        ExplorationNavigationStrategy(),
    ]

    for strategy in strategies:
        print(f"\n🧪 测试导航策略: {strategy.get_name()} (使用数据集迷宫)")

        # 初始化仿真
        validator.initialize_simulation(strategy, maze_complexity=0.7)

        # 运行仿真（包含视频录制）
        results = validator.run_simulation(max_steps=150, record_video=True)

        # 可视化结果
        save_path = f"results_dataset_{strategy.get_name().lower()}.png"
        validator.visualize_results(save_path)

        # 导出结果
        json_path = f"results_dataset_{strategy.get_name().lower()}.json"
        validator.export_results(json_path)

        print(f"✅ 数据集迷宫演示完成！结果已保存到 {save_path} 和 {json_path}")


def test_multiple_dataset_mazes():
    """测试多个数据集迷宫"""
    print("=" * 60)
    print("多个数据集迷宫测试")
    print("=" * 60)

    # 测试5个不同的数据集迷宫
    for i in range(5):
        print(f"\n🎯 测试第 {i+1} 个随机数据集迷宫")

        # 每次创建新的验证器以获取不同的随机迷宫
        validator = MultiAgentSLAMValidator(maze_size=15, num_agents=3, use_dataset_maze=True)

        # 使用探索策略
        strategy = ExplorationNavigationStrategy()

        # 初始化仿真
        validator.initialize_simulation(strategy, maze_complexity=0.7)

        # 运行短时间仿真
        results = validator.run_simulation(max_steps=100, record_video=False)

        # 导出结果
        json_path = f"results_dataset_test_{i+1}.json"
        validator.export_results(json_path)

        print(f"✅ 测试 {i+1} 完成！结果已保存到 {json_path}")


if __name__ == "__main__":
    import sys

    if len(sys.argv) > 1:
        if sys.argv[1] == "dataset":
            run_dataset_maze_demo()
        elif sys.argv[1] == "test_multiple":
            test_multiple_dataset_mazes()
        elif sys.argv[1] == "dataset_demo":
            run_validation_demo(use_dataset_maze=True)
        else:
            run_validation_demo()
    else:
        run_validation_demo()
