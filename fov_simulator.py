# -*- coding: utf-8 -*-
# File: fov_simulator.py
#!/usr/bin/env python3
"""
FOV视野模拟器 - 模拟智能体的75度视野范围观察
FOV Simulator - Simulates agent's 75-degree field of view observation
"""

import numpy as np
import math
from typing import Tuple, List


class FOVSimulator:
    """FOV视野模拟器"""
    
    def __init__(self, maze_size: int, fov_angle: float = 75.0, max_range: float = 8.0):
        self.maze_size = maze_size
        self.fov_angle = math.radians(fov_angle)  # 转换为弧度
        self.max_range = max_range  # 最大观察距离
    
    def get_fov_observation(self, position: np.ndarray, direction: np.ndarray,
                           ground_truth_maze: np.ndarray) -> np.ndarray:
        """
        获取智能体FOV观察
        Args:
            position: 智能体位置 [x, y]
            direction: 智能体朝向向量 [dx, dy]
            ground_truth_maze: 真实地图
        Returns:
            local_map: FOV观察到的地图，-1为未知，0为墙壁，1为通路
        """
        # 初始化为全未知
        local_map = np.full((self.maze_size, self.maze_size), -1, dtype=np.int8)

        # 计算朝向角度
        facing_angle = math.atan2(direction[1], direction[0])

        # FOV角度范围
        start_angle = facing_angle - self.fov_angle / 2
        end_angle = facing_angle + self.fov_angle / 2

        # 对FOV范围内的每个像素进行射线投射
        for y in range(self.maze_size):
            for x in range(self.maze_size):
                # 计算到目标点的角度和距离
                dx = x + 0.5 - position[0]  # 像素中心坐标
                dy = y + 0.5 - position[1]

                distance = math.sqrt(dx * dx + dy * dy)
                angle = math.atan2(dy, dx)

                # 检查是否在FOV范围内
                if self._is_angle_in_fov(angle, start_angle, end_angle) and distance <= self.max_range:
                    # 使用射线投射检查可见性
                    if self._is_point_visible(position, np.array([x + 0.5, y + 0.5]), ground_truth_maze):
                        local_map[y, x] = ground_truth_maze[y, x]

        return local_map

    def update_slam_map(self, current_slam_map: np.ndarray, position: np.ndarray,
                       direction: np.ndarray, ground_truth_maze: np.ndarray) -> np.ndarray:
        """
        更新SLAM地图（增量式更新，保留历史观察）
        Args:
            current_slam_map: 当前的SLAM累积地图
            position: 智能体位置 [x, y]
            direction: 智能体朝向向量 [dx, dy]
            ground_truth_maze: 真实地图
        Returns:
            updated_slam_map: 更新后的SLAM地图
        """
        # 获取当前FOV观察
        current_fov = self.get_fov_observation(position, direction, ground_truth_maze)

        # 复制当前SLAM地图
        updated_slam_map = current_slam_map.copy()

        # 将新观察融合到SLAM地图中
        # 只更新当前FOV中观察到的区域（非-1的区域）
        mask = current_fov != -1
        updated_slam_map[mask] = current_fov[mask]

        return updated_slam_map
    
    def _is_angle_in_fov(self, angle: float, start_angle: float, end_angle: float) -> bool:
        """检查角度是否在FOV范围内"""
        # 标准化角度到[-π, π]
        angle = self._normalize_angle(angle)
        start_angle = self._normalize_angle(start_angle)
        end_angle = self._normalize_angle(end_angle)
        
        if start_angle <= end_angle:
            return start_angle <= angle <= end_angle
        else:
            # 处理跨越-π/π边界的情况
            return angle >= start_angle or angle <= end_angle
    
    def _normalize_angle(self, angle: float) -> float:
        """标准化角度到[-π, π]范围"""
        while angle > math.pi:
            angle -= 2 * math.pi
        while angle < -math.pi:
            angle += 2 * math.pi
        return angle
    
    def _is_point_visible(self, observer_pos: np.ndarray, target_pos: np.ndarray, 
                         ground_truth_maze: np.ndarray) -> bool:
        """
        使用射线投射检查目标点是否可见（不被墙壁遮挡）
        Args:
            observer_pos: 观察者位置
            target_pos: 目标点位置
            ground_truth_maze: 真实地图
        Returns:
            bool: 是否可见
        """
        # 使用DDA算法进行射线投射
        line_points = self._get_line_points(observer_pos, target_pos)
        
        for point in line_points[:-1]:  # 不包括目标点本身
            grid_x = int(point[0])
            grid_y = int(point[1])
            
            # 边界检查
            if not (0 <= grid_x < self.maze_size and 0 <= grid_y < self.maze_size):
                return False
            
            # 如果射线经过墙壁，则目标点不可见
            if ground_truth_maze[grid_y, grid_x] == 0:
                return False
        
        return True
    
    def _get_line_points(self, start: np.ndarray, end: np.ndarray) -> List[np.ndarray]:
        """
        使用Bresenham算法获取两点之间的所有网格点
        Args:
            start: 起始点
            end: 结束点
        Returns:
            List[np.ndarray]: 线上的所有点
        """
        points = []
        
        x0, y0 = int(start[0]), int(start[1])
        x1, y1 = int(end[0]), int(end[1])
        
        dx = abs(x1 - x0)
        dy = abs(y1 - y0)
        
        x_step = 1 if x0 < x1 else -1
        y_step = 1 if y0 < y1 else -1
        
        error = dx - dy
        
        x, y = x0, y0
        
        while True:
            points.append(np.array([x, y]))
            
            if x == x1 and y == y1:
                break
            
            error2 = 2 * error
            
            if error2 > -dy:
                error -= dy
                x += x_step
            
            if error2 < dx:
                error += dx
                y += y_step
        
        return points
    
    def visualize_fov(self, position: np.ndarray, direction: np.ndarray, 
                     ground_truth_maze: np.ndarray, local_map: np.ndarray = None) -> None:
        """
        可视化FOV观察结果
        Args:
            position: 智能体位置
            direction: 智能体朝向
            ground_truth_maze: 真实地图
            local_map: FOV观察结果（可选）
        """
        import matplotlib.pyplot as plt
        
        if local_map is None:
            local_map = self.get_fov_observation(position, direction, ground_truth_maze)
        
        fig, axes = plt.subplots(1, 3, figsize=(15, 5))
        
        # 显示真实地图
        axes[0].imshow(ground_truth_maze, cmap='gray', origin='lower')
        axes[0].plot(position[0], position[1], 'ro', markersize=8)
        
        # 绘制朝向箭头
        arrow_length = 2.0
        end_x = position[0] + direction[0] * arrow_length
        end_y = position[1] + direction[1] * arrow_length
        axes[0].arrow(position[0], position[1], 
                     direction[0] * arrow_length, direction[1] * arrow_length,
                     head_width=0.3, head_length=0.3, fc='red', ec='red')
        
        # 绘制FOV扇形
        self._draw_fov_sector(axes[0], position, direction)
        axes[0].set_title('Ground Truth + FOV')
        axes[0].grid(True, alpha=0.3)
        
        # 显示FOV观察结果
        fov_display = np.where(local_map == -1, 0.5, local_map)  # 未知区域显示为灰色
        axes[1].imshow(fov_display, cmap='gray', origin='lower', vmin=0, vmax=1)
        axes[1].plot(position[0], position[1], 'ro', markersize=8)
        axes[1].set_title('FOV Observation')
        axes[1].grid(True, alpha=0.3)
        
        # 显示重叠对比
        axes[2].imshow(ground_truth_maze, cmap='gray', origin='lower', alpha=0.5)
        
        # 用不同颜色显示FOV观察到的区域
        known_mask = local_map != -1
        observed_values = local_map[known_mask]
        y_coords, x_coords = np.where(known_mask)
        
        # 正确观察的点（绿色）和错误观察的点（红色）
        for i, (y, x) in enumerate(zip(y_coords, x_coords)):
            if observed_values[i] == ground_truth_maze[y, x]:
                axes[2].plot(x, y, 'go', markersize=3, alpha=0.7)  # 正确观察
            else:
                axes[2].plot(x, y, 'ro', markersize=3, alpha=0.7)  # 错误观察（不应该发生）
        
        axes[2].plot(position[0], position[1], 'bo', markersize=8)
        axes[2].set_title('Observation Accuracy')
        axes[2].grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.show()
    
    def _draw_fov_sector(self, ax, position: np.ndarray, direction: np.ndarray) -> None:
        """在图上绘制FOV扇形"""
        facing_angle = math.atan2(direction[1], direction[0])
        start_angle = facing_angle - self.fov_angle / 2
        end_angle = facing_angle + self.fov_angle / 2
        
        # 创建扇形的点
        angles = np.linspace(start_angle, end_angle, 50)
        sector_x = [position[0]]
        sector_y = [position[1]]
        
        for angle in angles:
            x = position[0] + self.max_range * math.cos(angle)
            y = position[1] + self.max_range * math.sin(angle)
            sector_x.append(x)
            sector_y.append(y)
        
        sector_x.append(position[0])
        sector_y.append(position[1])
        
        ax.fill(sector_x, sector_y, color='yellow', alpha=0.3, label='FOV')
        ax.plot(sector_x, sector_y, 'y-', alpha=0.7)
    
    def get_fov_coverage(self, position: np.ndarray, direction: np.ndarray, 
                        ground_truth_maze: np.ndarray) -> float:
        """
        计算FOV覆盖率
        Args:
            position: 智能体位置
            direction: 智能体朝向
            ground_truth_maze: 真实地图
        Returns:
            float: 覆盖率 [0, 1]
        """
        local_map = self.get_fov_observation(position, direction, ground_truth_maze)
        
        total_fov_cells = self._count_fov_cells(position, direction)
        observed_cells = np.sum(local_map != -1)
        
        return observed_cells / total_fov_cells if total_fov_cells > 0 else 0.0
    
    def _count_fov_cells(self, position: np.ndarray, direction: np.ndarray) -> int:
        """计算FOV范围内的总格子数"""
        facing_angle = math.atan2(direction[1], direction[0])
        start_angle = facing_angle - self.fov_angle / 2
        end_angle = facing_angle + self.fov_angle / 2
        
        count = 0
        for y in range(self.maze_size):
            for x in range(self.maze_size):
                dx = x + 0.5 - position[0]
                dy = y + 0.5 - position[1]
                
                distance = math.sqrt(dx * dx + dy * dy)
                angle = math.atan2(dy, dx)
                
                if self._is_angle_in_fov(angle, start_angle, end_angle) and distance <= self.max_range:
                    count += 1
        
        return count


if __name__ == "__main__":
    # 测试FOV模拟器
    maze_size = 15
    
    # 创建测试迷宫
    test_maze = np.ones((maze_size, maze_size), dtype=np.uint8)
    # 添加一些墙壁
    test_maze[5:8, 5:8] = 0
    test_maze[10:12, 3:6] = 0
    test_maze[2:4, 10:13] = 0
    
    # 创建FOV模拟器
    fov_simulator = FOVSimulator(maze_size, fov_angle=75, max_range=6)
    
    # 测试不同位置和朝向
    test_cases = [
        (np.array([7.5, 7.5]), np.array([1.0, 0.0])),    # 中心，朝右
        (np.array([3.0, 3.0]), np.array([0.7, 0.7])),    # 左下，朝右上
        (np.array([12.0, 12.0]), np.array([-1.0, 0.0])), # 右上，朝左
    ]
    
    for i, (position, direction) in enumerate(test_cases):
        print(f"\n测试案例 {i+1}: 位置{position}, 朝向{direction}")
        
        # 获取FOV观察
        local_map = fov_simulator.get_fov_observation(position, direction, test_maze)
        
        # 计算统计信息
        observed_cells = np.sum(local_map != -1)
        wall_cells = np.sum(local_map == 0)
        path_cells = np.sum(local_map == 1)
        coverage = fov_simulator.get_fov_coverage(position, direction, test_maze)
        
        print(f"  观察到的格子: {observed_cells}")
        print(f"  墙壁格子: {wall_cells}")
        print(f"  通路格子: {path_cells}")
        print(f"  FOV覆盖率: {coverage:.2%}")
        
        # 可视化
        fov_simulator.visualize_fov(position, direction, test_maze, local_map)