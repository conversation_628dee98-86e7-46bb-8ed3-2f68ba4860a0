# -*- coding: utf-8 -*-
# File: reward_calculator.py
#!/usr/bin/env python3
"""
奖励计算器 - 计算多智能体协同SLAM的奖励机制
Reward Calculator - Calculates rewards for multi-agent collaborative SLAM
"""

import numpy as np
from typing import List, Dict
from agent_manager import AgentState, SectorManager


class RewardCalculator:
    """奖励计算器"""
    
    def __init__(self, sector_manager: SectorManager):
        self.sector_manager = sector_manager
        
        # 奖励权重参数
        self.exploration_weight = 10.0      # 探索奖励权重
        self.sector_penalty_weight = 5.0    # 区域惩罚权重
        self.collaboration_weight = 2.0     # 协同奖励权重
        self.efficiency_weight = 1.0        # 效率奖励权重
        
        # 历史状态跟踪
        self.previous_exploration_areas = {}
    
    def calculate_reward(self, agent_state: AgentState, all_agents: List[AgentState]) -> float:
        """
        计算智能体奖励
        Args:
            agent_state: 当前智能体状态
            all_agents: 所有智能体状态列表
        Returns:
            reward: 总奖励值
        """
        reward = 0.0
        
        # 1. 探索奖励 - 基于新探索的区域
        exploration_reward = self._calculate_exploration_reward(agent_state)
        reward += exploration_reward
        
        # 2. 区域惩罚 - 在其他智能体区域活动的惩罚
        sector_penalty = self._calculate_sector_penalty(agent_state)
        reward -= sector_penalty
        
        # 3. 协同奖励 - 基于地图融合的收益
        collaboration_reward = self._calculate_collaboration_reward(agent_state, all_agents)
        reward += collaboration_reward
        
        # 4. 效率奖励 - 基于探索效率
        efficiency_reward = self._calculate_efficiency_reward(agent_state)
        reward += efficiency_reward
        
        return reward
    
    def _calculate_exploration_reward(self, agent_state: AgentState) -> float:
        """计算探索奖励"""
        # 使用累积SLAM地图计算探索面积
        current_exploration = np.sum(agent_state.slam_map != -1)

        # 获取上一步的探索面积
        previous_exploration = self.previous_exploration_areas.get(agent_state.agent_id, 0)

        # 新探索的面积
        new_exploration = max(0, current_exploration - previous_exploration)

        # 更新历史记录
        self.previous_exploration_areas[agent_state.agent_id] = current_exploration

        # 探索奖励：新探索的面积 * 权重
        exploration_reward = new_exploration * self.exploration_weight

        return exploration_reward
    
    def _calculate_sector_penalty(self, agent_state: AgentState) -> float:
        """计算区域惩罚"""
        if not self.sector_manager.is_in_own_sector(agent_state.agent_id, agent_state.position):
            return self.sector_penalty_weight
        return 0.0
    
    def _calculate_collaboration_reward(self, agent_state: AgentState, all_agents: List[AgentState]) -> float:
        """计算协同奖励"""
        # 基于融合地图相比单独SLAM地图的信息增益
        individual_known = np.sum(agent_state.slam_map != -1)
        fused_known = np.sum(agent_state.fused_map != -1)

        # 信息增益
        information_gain = fused_known - individual_known

        # 协同奖励：信息增益 * 权重
        collaboration_reward = information_gain * self.collaboration_weight

        return collaboration_reward
    
    def _calculate_efficiency_reward(self, agent_state: AgentState) -> float:
        """计算效率奖励"""
        if agent_state.step_count == 0:
            return 0.0

        # 探索效率：每步探索的新区域（基于累积SLAM地图）
        current_exploration = np.sum(agent_state.slam_map != -1)
        efficiency = current_exploration / agent_state.step_count

        # 效率奖励
        efficiency_reward = efficiency * self.efficiency_weight

        return efficiency_reward
    
    def calculate_team_reward(self, all_agents: List[AgentState]) -> Dict[str, float]:
        """
        计算团队级别的奖励
        Args:
            all_agents: 所有智能体状态列表
        Returns:
            Dict: 团队奖励统计
        """
        # 计算全局融合地图（基于累积SLAM地图）
        global_fused_map = np.full((self.sector_manager.maze_size, self.sector_manager.maze_size), -1, dtype=np.int8)
        for agent_state in all_agents:
            mask = agent_state.slam_map != -1
            global_fused_map[mask] = agent_state.slam_map[mask]
        
        # 团队探索覆盖率
        total_known = np.sum(global_fused_map != -1)
        total_cells = self.sector_manager.maze_size * self.sector_manager.maze_size
        coverage_rate = total_known / total_cells
        
        # 探索分布均匀性（各智能体探索面积的标准差）
        exploration_areas = [np.sum(agent.slam_map != -1) for agent in all_agents]
        exploration_std = np.std(exploration_areas)
        exploration_balance = 1.0 / (1.0 + exploration_std)  # 标准差越小，均匀性越好
        
        # 区域违规率
        violations = sum(1 for agent in all_agents 
                        if not self.sector_manager.is_in_own_sector(agent.agent_id, agent.position))
        violation_rate = violations / len(all_agents)
        
        # 协作效率（重叠区域 vs 独有区域）
        overlap_count = 0
        unique_count = 0
        
        for i, agent1 in enumerate(all_agents):
            for j, agent2 in enumerate(all_agents):
                if i < j:
                    mask1 = agent1.local_map != -1
                    mask2 = agent2.local_map != -1
                    overlap = np.sum(mask1 & mask2)
                    unique1 = np.sum(mask1 & (~mask2))
                    unique2 = np.sum(mask2 & (~mask1))
                    
                    overlap_count += overlap
                    unique_count += unique1 + unique2
        
        collaboration_efficiency = unique_count / (unique_count + overlap_count) if (unique_count + overlap_count) > 0 else 0.0
        
        return {
            'coverage_rate': coverage_rate,
            'exploration_balance': exploration_balance,
            'violation_rate': violation_rate,
            'collaboration_efficiency': collaboration_efficiency,
            'total_known_cells': total_known,
            'team_size': len(all_agents)
        }
    
    def calculate_adaptive_rewards(self, agent_state: AgentState, all_agents: List[AgentState], 
                                 performance_history: Dict) -> float:
        """
        自适应奖励计算 - 根据历史表现调整奖励权重
        Args:
            agent_state: 当前智能体状态
            all_agents: 所有智能体状态列表
            performance_history: 历史表现数据
        Returns:
            adaptive_reward: 自适应奖励
        """
        base_reward = self.calculate_reward(agent_state, all_agents)
        
        # 根据历史表现调整权重
        if performance_history:
            # 如果探索进度缓慢，增加探索奖励权重
            recent_coverage = performance_history.get('recent_coverage_growth', 0.0)
            if recent_coverage < 0.01:  # 覆盖率增长缓慢
                exploration_boost = 2.0
            else:
                exploration_boost = 1.0
            
            # 如果区域违规过多，增加惩罚权重
            recent_violations = performance_history.get('recent_violation_rate', 0.0)
            if recent_violations > 0.3:  # 违规率过高
                sector_penalty_boost = 2.0
            else:
                sector_penalty_boost = 1.0
            
            # 调整奖励
            adaptive_reward = base_reward * exploration_boost
            if not self.sector_manager.is_in_own_sector(agent_state.agent_id, agent_state.position):
                adaptive_reward -= self.sector_penalty_weight * sector_penalty_boost
        else:
            adaptive_reward = base_reward
        
        return adaptive_reward
    
    def get_reward_breakdown(self, agent_state: AgentState, all_agents: List[AgentState]) -> Dict[str, float]:
        """
        获取奖励组成的详细分解
        Args:
            agent_state: 当前智能体状态
            all_agents: 所有智能体状态列表
        Returns:
            Dict: 奖励分解
        """
        exploration_reward = self._calculate_exploration_reward(agent_state)
        sector_penalty = self._calculate_sector_penalty(agent_state)
        collaboration_reward = self._calculate_collaboration_reward(agent_state, all_agents)
        efficiency_reward = self._calculate_efficiency_reward(agent_state)
        
        total_reward = exploration_reward - sector_penalty + collaboration_reward + efficiency_reward
        
        return {
            'exploration_reward': exploration_reward,
            'sector_penalty': sector_penalty,
            'collaboration_reward': collaboration_reward,
            'efficiency_reward': efficiency_reward,
            'total_reward': total_reward
        }
    
    def update_reward_parameters(self, new_weights: Dict[str, float]):
        """
        更新奖励参数
        Args:
            new_weights: 新的权重参数
        """
        if 'exploration_weight' in new_weights:
            self.exploration_weight = new_weights['exploration_weight']
        if 'sector_penalty_weight' in new_weights:
            self.sector_penalty_weight = new_weights['sector_penalty_weight']
        if 'collaboration_weight' in new_weights:
            self.collaboration_weight = new_weights['collaboration_weight']
        if 'efficiency_weight' in new_weights:
            self.efficiency_weight = new_weights['efficiency_weight']
    
    def reset_history(self):
        """重置历史状态"""
        self.previous_exploration_areas.clear()
    
    def visualize_reward_trends(self, reward_history: List[Dict], save_path: str = "reward_trends.png"):
        """可视化奖励趋势"""
        import matplotlib.pyplot as plt
        
        if not reward_history:
            print("没有奖励历史数据可视化")
            return
        
        # 提取不同类型的奖励数据
        steps = list(range(len(reward_history)))
        exploration_rewards = [entry.get('exploration_reward', 0) for entry in reward_history]
        sector_penalties = [entry.get('sector_penalty', 0) for entry in reward_history]
        collaboration_rewards = [entry.get('collaboration_reward', 0) for entry in reward_history]
        efficiency_rewards = [entry.get('efficiency_reward', 0) for entry in reward_history]
        total_rewards = [entry.get('total_reward', 0) for entry in reward_history]
        
        # 创建子图
        fig, axes = plt.subplots(2, 2, figsize=(12, 8))
        
        # 探索奖励
        axes[0, 0].plot(steps, exploration_rewards, 'g-', linewidth=2, label='Exploration Reward')
        axes[0, 0].set_title('Exploration Rewards')
        axes[0, 0].set_xlabel('Steps')
        axes[0, 0].set_ylabel('Reward')
        axes[0, 0].grid(True, alpha=0.3)
        axes[0, 0].legend()
        
        # 区域惩罚
        axes[0, 1].plot(steps, sector_penalties, 'r-', linewidth=2, label='Sector Penalty')
        axes[0, 1].set_title('Sector Penalties')
        axes[0, 1].set_xlabel('Steps')
        axes[0, 1].set_ylabel('Penalty')
        axes[0, 1].grid(True, alpha=0.3)
        axes[0, 1].legend()
        
        # 协同奖励
        axes[1, 0].plot(steps, collaboration_rewards, 'b-', linewidth=2, label='Collaboration Reward')
        axes[1, 0].set_title('Collaboration Rewards')
        axes[1, 0].set_xlabel('Steps')
        axes[1, 0].set_ylabel('Reward')
        axes[1, 0].grid(True, alpha=0.3)
        axes[1, 0].legend()
        
        # 总奖励
        axes[1, 1].plot(steps, total_rewards, 'k-', linewidth=2, label='Total Reward')
        axes[1, 1].plot(steps, exploration_rewards, 'g--', alpha=0.7, label='Exploration')
        axes[1, 1].plot(steps, [-p for p in sector_penalties], 'r--', alpha=0.7, label='Sector Penalty')
        axes[1, 1].plot(steps, collaboration_rewards, 'b--', alpha=0.7, label='Collaboration')
        axes[1, 1].set_title('Total Rewards (Components)')
        axes[1, 1].set_xlabel('Steps')
        axes[1, 1].set_ylabel('Reward')
        axes[1, 1].grid(True, alpha=0.3)
        axes[1, 1].legend()
        
        plt.tight_layout()
        plt.savefig(save_path, dpi=150, bbox_inches='tight')
        plt.close()
        print(f"奖励趋势图已保存: {save_path}")


if __name__ == "__main__":
    # 测试奖励计算器
    from agent_manager import AgentManager, AgentState
    import numpy as np
    
    # 创建测试环境
    maze_size = 10
    num_agents = 3
    
    # 创建智能体管理器
    agent_manager = AgentManager(maze_size, num_agents)
    
    # 创建测试迷宫
    test_maze = np.ones((maze_size, maze_size), dtype=np.uint8)
    test_maze[3:6, 3:6] = 0  # 添加障碍物
    
    # 初始化智能体
    agent_manager.initialize_agents(test_maze)
    
    # 创建奖励计算器
    reward_calculator = RewardCalculator(agent_manager.sector_manager)
    
    # 模拟一些探索数据
    for step in range(10):
        for i, agent_state in enumerate(agent_manager.agent_states):
            # 模拟探索新区域
            if step > 0:
                # 随机添加一些新的观察
                for _ in range(2):
                    x = np.random.randint(0, maze_size)
                    y = np.random.randint(0, maze_size)
                    if agent_state.local_map[y, x] == -1:  # 如果是未知区域
                        agent_state.local_map[y, x] = test_maze[y, x]
            
            # 随机移动位置（可能违规）
            if np.random.random() < 0.3:  # 30%概率移动到其他区域
                agent_state.position = np.random.uniform([1, 1], [maze_size-1, maze_size-1])
            
            agent_state.step_count += 1
        
        # 更新融合地图
        agent_manager.fuse_all_maps()
        
        # 计算奖励
        print(f"\nStep {step}:")
        for i, agent_state in enumerate(agent_manager.agent_states):
            reward_breakdown = reward_calculator.get_reward_breakdown(
                agent_state, agent_manager.agent_states
            )
            
            print(f"Agent {i}: Total={reward_breakdown['total_reward']:.2f} "
                  f"(Exploration={reward_breakdown['exploration_reward']:.2f}, "
                  f"Penalty={reward_breakdown['sector_penalty']:.2f}, "
                  f"Collaboration={reward_breakdown['collaboration_reward']:.2f})")
        
        # 计算团队奖励
        team_reward = reward_calculator.calculate_team_reward(agent_manager.agent_states)
        print(f"Team Coverage: {team_reward['coverage_rate']:.3f}, "
              f"Balance: {team_reward['exploration_balance']:.3f}, "
              f"Violations: {team_reward['violation_rate']:.3f}")
    
    print("\n奖励计算器测试完成!")
