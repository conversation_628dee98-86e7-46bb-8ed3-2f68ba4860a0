#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据集迷宫演示脚本
Dataset Maze Demo Script

这个脚本演示如何使用数据集中的迷宫布局进行多智能体SLAM仿真
This script demonstrates how to use maze layouts from the dataset for multi-agent SLAM simulation
"""

from main_validator import MultiAgentSLAMValidator
from navigation_strategies import RandomNavigationStrategy, ExplorationNavigationStrategy


def demo_single_dataset_maze():
    """演示单个数据集迷宫的使用"""
    print("=" * 60)
    print("单个数据集迷宫演示")
    print("=" * 60)
    
    # 创建使用数据集迷宫的验证器
    # use_dataset_maze=True 表示从数据集随机选择迷宫
    validator = MultiAgentSLAMValidator(
        maze_size=15,           # 迷宫大小 15x15
        num_agents=3,           # 3个智能体
        seed=42,                # 随机种子，确保可重复性
        use_dataset_maze=True   # 使用数据集迷宫
    )
    
    # 选择导航策略
    strategy = ExplorationNavigationStrategy()
    
    print(f"使用导航策略: {strategy.get_name()}")
    
    # 初始化仿真
    validator.initialize_simulation(strategy, maze_complexity=0.7)
    
    # 运行仿真
    results = validator.run_simulation(max_steps=150, record_video=True)
    
    # 可视化结果
    save_path = "demo_dataset_results.png"
    validator.visualize_results(save_path)
    
    # 导出结果
    json_path = "demo_dataset_results.json"
    validator.export_results(json_path)
    
    # 打印统计信息
    print(f"\n📊 仿真结果:")
    print(f"   总奖励: {sum(state.reward_score for state in validator.agent_manager.agent_states):.2f}")
    print(f"   探索进度: {results['exploration_progress'][-1]:.3f}")
    if results['correlation_history']:
        final_corr = results['correlation_history'][-1]['mean_correlation']
        print(f"   最终平均相关性: {final_corr:.3f}")
    print(f"   区域违规总数: {sum(results['sector_violations'])}")
    
    print(f"\n✅ 演示完成！")
    print(f"生成的文件:")
    print(f"  - {save_path} (结果可视化)")
    print(f"  - {json_path} (详细数据)")
    print(f"  - simulation_*.mp4 (仿真视频)")


def demo_multiple_dataset_mazes():
    """演示多个数据集迷宫的使用"""
    print("=" * 60)
    print("多个数据集迷宫演示")
    print("=" * 60)
    
    # 测试3个不同的数据集迷宫
    for i in range(3):
        print(f"\n🎯 演示第 {i+1} 个数据集迷宫")
        
        # 每次创建新的验证器以获取不同的随机迷宫
        validator = MultiAgentSLAMValidator(
            maze_size=15,
            num_agents=3,
            use_dataset_maze=True  # 每次都会随机选择不同的数据集迷宫
        )
        
        # 使用探索策略
        strategy = ExplorationNavigationStrategy()
        
        # 初始化仿真
        validator.initialize_simulation(strategy, maze_complexity=0.7)
        
        # 运行较短的仿真
        results = validator.run_simulation(max_steps=100, record_video=False)
        
        # 导出结果
        json_path = f"demo_multiple_{i+1}.json"
        validator.export_results(json_path)
        
        # 打印简要统计
        total_reward = sum(state.reward_score for state in validator.agent_manager.agent_states)
        exploration = results['exploration_progress'][-1]
        print(f"   结果: 奖励={total_reward:.1f}, 探索进度={exploration:.3f}")
        print(f"   结果已保存到: {json_path}")
    
    print(f"\n✅ 多个数据集迷宫演示完成！")


def demo_strategy_comparison():
    """演示不同策略在数据集迷宫上的表现"""
    print("=" * 60)
    print("策略比较演示（使用数据集迷宫）")
    print("=" * 60)
    
    # 测试不同的导航策略
    strategies = [
        RandomNavigationStrategy(),
        ExplorationNavigationStrategy()
    ]
    
    results_summary = []
    
    for strategy in strategies:
        print(f"\n🧪 测试策略: {strategy.get_name()}")
        
        # 创建验证器（使用相同的种子确保使用相同的迷宫）
        validator = MultiAgentSLAMValidator(
            maze_size=15,
            num_agents=3,
            seed=123,  # 固定种子确保公平比较
            use_dataset_maze=True
        )
        
        # 初始化仿真
        validator.initialize_simulation(strategy, maze_complexity=0.7)
        
        # 运行仿真
        results = validator.run_simulation(max_steps=120, record_video=False)
        
        # 收集统计信息
        total_reward = sum(state.reward_score for state in validator.agent_manager.agent_states)
        exploration = results['exploration_progress'][-1]
        violations = sum(results['sector_violations'])
        
        strategy_result = {
            'strategy': strategy.get_name(),
            'total_reward': total_reward,
            'exploration_progress': exploration,
            'sector_violations': violations
        }
        results_summary.append(strategy_result)
        
        # 保存结果
        save_path = f"demo_strategy_{strategy.get_name().lower()}.png"
        validator.visualize_results(save_path)
        
        json_path = f"demo_strategy_{strategy.get_name().lower()}.json"
        validator.export_results(json_path)
        
        print(f"   结果已保存到: {save_path} 和 {json_path}")
    
    # 打印比较结果
    print(f"\n📈 策略比较结果:")
    print(f"{'策略':<15} {'总奖励':<12} {'探索进度':<12} {'违规次数':<12}")
    print("-" * 55)
    
    for result in results_summary:
        print(f"{result['strategy']:<15} {result['total_reward']:<12.1f} "
              f"{result['exploration_progress']:<12.3f} {result['sector_violations']:<12}")
    
    print(f"\n✅ 策略比较演示完成！")


def main():
    """主函数"""
    print("数据集迷宫演示程序")
    print("Dataset Maze Demo Program")
    print("=" * 60)
    
    print("可用的演示:")
    print("1. 单个数据集迷宫演示")
    print("2. 多个数据集迷宫演示") 
    print("3. 策略比较演示")
    print("4. 运行所有演示")
    
    choice = input("\n请选择演示 (1-4): ").strip()
    
    if choice == "1":
        demo_single_dataset_maze()
    elif choice == "2":
        demo_multiple_dataset_mazes()
    elif choice == "3":
        demo_strategy_comparison()
    elif choice == "4":
        demo_single_dataset_maze()
        demo_multiple_dataset_mazes()
        demo_strategy_comparison()
    else:
        print("无效选择，运行默认演示...")
        demo_single_dataset_maze()


if __name__ == "__main__":
    main()
