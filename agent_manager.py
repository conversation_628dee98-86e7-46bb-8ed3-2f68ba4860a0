# -*- coding: utf-8 -*-
# File: agent_manager.py
#!/usr/bin/env python3
"""
智能体管理器 - 管理多个智能体的状态和扇区分割
Agent Manager - Manages multiple agents' states and sector division
"""

import numpy as np
from typing import List, Dict, Tuple
from dataclasses import dataclass
import random


@dataclass
class AgentState:
    """智能体状态"""
    agent_id: int
    position: np.ndarray  # [x, y]
    direction: np.ndarray  # 朝向向量 [dx, dy]
    sector: int  # 所属扇区
    local_map: np.ndarray  # 本地地图观察 (-1: 未知, 0: 墙壁, 1: 通路) - 当前FOV观察
    slam_map: np.ndarray  # 累积SLAM地图 (-1: 未知, 0: 墙壁, 1: 通路) - 历史累积观察
    fused_map: np.ndarray  # 融合后的地图
    exploration_area: float  # 已探索面积
    reward_score: float  # 累计奖励分数
    step_count: int  # 步数统计


class SectorManager:
    """扇区管理器 - 将地图分割为N个扇区"""
    
    def __init__(self, maze_size: int, num_agents: int):
        self.maze_size = maze_size
        self.num_agents = num_agents
        self.sectors = self._create_sectors()
    
    def _create_sectors(self) -> List[np.ndarray]:
        """创建扇区分割 - 基于角度的扇形分割"""
        sectors = []
        center = np.array([self.maze_size / 2, self.maze_size / 2])
        
        for i in range(self.num_agents):
            sector_mask = np.zeros((self.maze_size, self.maze_size), dtype=bool)
            
            # 计算扇区角度范围
            angle_per_sector = 2 * np.pi / self.num_agents
            start_angle = i * angle_per_sector
            end_angle = (i + 1) * angle_per_sector
            
            # 为每个像素分配扇区
            for y in range(self.maze_size):
                for x in range(self.maze_size):
                    # 计算相对于中心的角度
                    dx = x - center[0]
                    dy = y - center[1]
                    
                    if dx == 0 and dy == 0:
                        # 中心点属于第一个扇区
                        if i == 0:
                            sector_mask[y, x] = True
                    else:
                        angle = np.arctan2(dy, dx)
                        # 将角度标准化到 [0, 2π]
                        angle = (angle + 2 * np.pi) % (2 * np.pi)
                        
                        # 检查是否在当前扇区内
                        if self._angle_in_sector(angle, start_angle, end_angle):
                            sector_mask[y, x] = True
            
            sectors.append(sector_mask)
        
        return sectors
    
    def _angle_in_sector(self, angle: float, start_angle: float, end_angle: float) -> bool:
        """检查角度是否在扇区内"""
        # 确保所有角度都在 [0, 2π] 范围内
        angle = angle % (2 * np.pi)
        start_angle = start_angle % (2 * np.pi)
        end_angle = end_angle % (2 * np.pi)
        
        if start_angle <= end_angle:
            return start_angle <= angle < end_angle
        else:
            # 处理跨越0角度的情况
            return angle >= start_angle or angle < end_angle
    
    def get_agent_sector(self, agent_id: int) -> np.ndarray:
        """获取智能体所属扇区"""
        return self.sectors[agent_id]
    
    def is_in_own_sector(self, agent_id: int, position: np.ndarray) -> bool:
        """检查位置是否在智能体自己的扇区内"""
        x, y = int(position[0]), int(position[1])
        if 0 <= x < self.maze_size and 0 <= y < self.maze_size:
            return self.sectors[agent_id][y, x]
        return False


class AgentManager:
    """智能体管理器"""
    
    def __init__(self, maze_size: int, num_agents: int):
        self.maze_size = maze_size
        self.num_agents = num_agents
        self.sector_manager = SectorManager(maze_size, num_agents)
        self.agent_states: List[AgentState] = []
    
    def initialize_agents(self, ground_truth_maze: np.ndarray) -> None:
        """初始化智能体 - 确保在通路上且不穿墙"""
        self.agent_states = []
        
        for i in range(self.num_agents):
            # 在各自扇区内寻找有效的初始位置
            valid_position = self._find_valid_position_in_sector(i, ground_truth_maze)
            
            # 随机初始朝向
            angle = random.uniform(0, 2 * np.pi)
            initial_direction = np.array([np.cos(angle), np.sin(angle)])
            
            # 创建智能体状态
            agent_state = AgentState(
                agent_id=i,
                position=valid_position,
                direction=initial_direction,
                sector=i,
                local_map=np.full((self.maze_size, self.maze_size), -1, dtype=np.int8),  # 当前FOV观察
                slam_map=np.full((self.maze_size, self.maze_size), -1, dtype=np.int8),   # 累积SLAM地图
                fused_map=np.full((self.maze_size, self.maze_size), -1, dtype=np.int8),
                exploration_area=0.0,
                reward_score=0.0,
                step_count=0
            )
            
            self.agent_states.append(agent_state)
        
        print(f"初始化了{len(self.agent_states)}个智能体")
        for i, state in enumerate(self.agent_states):
            print(f"  智能体{i}: 位置{state.position}, 扇区{state.sector}")
    
    def _find_valid_position_in_sector(self, agent_id: int, ground_truth_maze: np.ndarray) -> np.ndarray:
        """在指定扇区内找到有效的初始位置"""
        sector_mask = self.sector_manager.get_agent_sector(agent_id)
        
        # 找到扇区内的所有通路位置
        valid_positions = []
        for y in range(self.maze_size):
            for x in range(self.maze_size):
                if sector_mask[y, x] and ground_truth_maze[y, x] == 1:  # 在扇区内且是通路
                    valid_positions.append(np.array([x + 0.5, y + 0.5]))
        
        if valid_positions:
            # 随机选择一个有效位置
            return random.choice(valid_positions)
        else:
            # 如果扇区内没有通路，在整个地图中寻找
            print(f"警告: 智能体{agent_id}的扇区内没有通路，使用全图搜索")
            all_valid_positions = []
            for y in range(self.maze_size):
                for x in range(self.maze_size):
                    if ground_truth_maze[y, x] == 1:
                        all_valid_positions.append(np.array([x + 0.5, y + 0.5]))
            
            if all_valid_positions:
                return random.choice(all_valid_positions)
            else:
                # 最后的备选方案：使用地图中心
                return np.array([self.maze_size / 2, self.maze_size / 2])
    
    def fuse_all_maps(self) -> None:
        """融合所有智能体的地图"""
        # 创建全局融合地图
        global_fused_map = np.full((self.maze_size, self.maze_size), -1, dtype=np.int8)

        # 融合所有智能体的SLAM观察
        for agent_state in self.agent_states:
            mask = agent_state.slam_map != -1  # 已知区域
            global_fused_map[mask] = agent_state.slam_map[mask]

        # 更新每个智能体的融合地图
        for agent_state in self.agent_states:
            agent_state.fused_map = global_fused_map.copy()

            # 更新探索面积（基于累积SLAM地图）
            agent_state.exploration_area = np.sum(agent_state.slam_map != -1)
    
    def get_global_fused_map(self) -> np.ndarray:
        """获取全局融合地图"""
        global_fused_map = np.full((self.maze_size, self.maze_size), -1, dtype=np.int8)

        for agent_state in self.agent_states:
            mask = agent_state.slam_map != -1
            global_fused_map[mask] = agent_state.slam_map[mask]

        return global_fused_map
    
    def get_agent_distances(self) -> Dict[Tuple[int, int], float]:
        """计算智能体之间的距离"""
        distances = {}
        
        for i in range(len(self.agent_states)):
            for j in range(i + 1, len(self.agent_states)):
                pos1 = self.agent_states[i].position
                pos2 = self.agent_states[j].position
                distance = np.linalg.norm(pos1 - pos2)
                distances[(i, j)] = distance
        
        return distances
    
    def check_collisions(self) -> List[Tuple[int, int]]:
        """检查智能体碰撞"""
        collisions = []
        collision_threshold = 0.8  # 碰撞阈值
        
        for i in range(len(self.agent_states)):
            for j in range(i + 1, len(self.agent_states)):
                pos1 = self.agent_states[i].position
                pos2 = self.agent_states[j].position
                distance = np.linalg.norm(pos1 - pos2)
                
                if distance < collision_threshold:
                    collisions.append((i, j))
        
        return collisions
    
    def get_statistics(self) -> Dict:
        """获取智能体统计信息"""
        stats = {
            'num_agents': len(self.agent_states),
            'total_exploration': sum(state.exploration_area for state in self.agent_states),
            'total_rewards': sum(state.reward_score for state in self.agent_states),
            'average_steps': np.mean([state.step_count for state in self.agent_states]),
            'agent_details': []
        }
        
        for state in self.agent_states:
            agent_detail = {
                'agent_id': state.agent_id,
                'position': state.position.tolist(),
                'sector': state.sector,
                'exploration_area': state.exploration_area,
                'reward_score': state.reward_score,
                'step_count': state.step_count
            }
            stats['agent_details'].append(agent_detail)
        
        return stats
    
    def reset_rewards(self) -> None:
        """重置所有智能体的奖励"""
        for state in self.agent_states:
            state.reward_score = 0.0
    
    def update_agent_position(self, agent_id: int, new_position: np.ndarray, new_direction: np.ndarray) -> bool:
        """更新智能体位置（带有效性检查）"""
        if 0 <= agent_id < len(self.agent_states):
            # 边界检查
            if (0.5 <= new_position[0] <= self.maze_size - 0.5 and
                0.5 <= new_position[1] <= self.maze_size - 0.5):
                
                self.agent_states[agent_id].position = new_position
                self.agent_states[agent_id].direction = new_direction
                return True
        
        return False


if __name__ == "__main__":
    # 测试智能体管理器
    import matplotlib.pyplot as plt
    
    # 创建测试迷宫
    maze_size = 15
    num_agents = 3
    test_maze = np.ones((maze_size, maze_size), dtype=np.uint8)
    
    # 添加一些墙壁
    test_maze[5:8, 5:8] = 0
    test_maze[10:12, 3:6] = 0
    
    # 创建智能体管理器
    agent_manager = AgentManager(maze_size, num_agents)
    agent_manager.initialize_agents(test_maze)
    
    # 可视化
    fig, axes = plt.subplots(1, 2, figsize=(12, 5))
    
    # 显示迷宫和智能体位置
    axes[0].imshow(test_maze, cmap='gray', origin='lower')
    colors = ['red', 'blue', 'green', 'orange']
    for i, state in enumerate(agent_manager.agent_states):
        axes[0].plot(state.position[0], state.position[1], 'o', 
                    color=colors[i], markersize=10, label=f'Agent {i}')
    axes[0].set_title('Maze and Agent Positions')
    axes[0].legend()
    
    # 显示扇区分割
    sector_viz = np.zeros((maze_size, maze_size, 3))
    colors_rgb = [(1,0,0), (0,1,0), (0,0,1), (1,1,0)]
    for i, sector_mask in enumerate(agent_manager.sector_manager.sectors):
        sector_viz[sector_mask] = colors_rgb[i]
    
    axes[1].imshow(sector_viz, origin='lower')
    axes[1].set_title('Sector Division')
    
    plt.tight_layout()
    plt.show()
    
    # 打印统计信息
    stats = agent_manager.get_statistics()
    print("智能体统计信息:")
    for detail in stats['agent_details']:
        print(f"  智能体{detail['agent_id']}: 位置{detail['position']}, 扇区{detail['sector']}")
